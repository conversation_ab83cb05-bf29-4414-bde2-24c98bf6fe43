<div id="r4w_system_message"></div>
<div id="r4w_auth_forgot">
	<div class="css-100frqb">
		<div class="css-9388ij">
		  <div class="css-1lekzkb">
		      <div class="css-1rvpz4w">
		        [@svg_rank4win_black]
		      </div>
		      <a class="css-1rvpz4w" href="[@url_create_account]">
		        {_('Dont have an account')}? 
		        <b>{_('Sign up here')}.</b>
		      </a>
		  </div>
		  <div class="css-12kz5vy">
		      <p class="css-c320wn e178wkxy0">{_('Please enter your email address and we will send you an email to reset your password')}.</p>
		      <div id="r4w_error"></div>
	          <div name="user" class="css-4ywf4y">
	              <label for="user" class="css-ya34dr">{_('Email Address')}</label>
	              <div>
	                  <input id="user" name="user" placeholder="[@placeholder_email]" class="css-1rkhnpn" value="">
	              </div>
	          </div>
	          <div id="loading"><div class="dual-ring"></div></div>
	          <button id="btn_submit" class="css-1ux7ccw">{_('Reset Password')}</button>
		  </div>
		  <div>
		    <a class="css-1rvpz4w" href="[@url_login]">{_('Already have an account')} ?
		      <span class="css-ck81h2">{_('Login here')}.</span>
		    </a>
		  </div>
		</div>
	</div>
</div>