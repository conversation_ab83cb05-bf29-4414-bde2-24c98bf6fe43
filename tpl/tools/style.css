.rank4win_page_r4w_tools #wpbody,.rank4win_page_r4w_tools #wpbody-content,#r4w_box_tools,#r4w_box_tools .tab_container,#r4w_box_tools .wrap{
    height:100%;
    position: relative;
    display: flex;
    flex-flow: column;
}
#wpbody-content {
    padding-bottom: 20px;
}
#r4w_box_tools .box_cnt{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    display: flex;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
}
#r4w_box_tools .css-df5dsf0effet {
    font-size: 22px;
    font-weight: 600;
    width: 100%;
    color: #444;
    margin: auto;
}
#r4w_box_tools .css-fds2f0eef {
    width: 50%;
    margin: auto;
}
#r4w_box_tools .css-df5e0zfezt{
    display:none;
    background: #FA6742;
    padding: 8px;
    color: #fff;
    width: max-content;
    margin: auto;
    margin-right: 0;
    cursor: pointer;
    font-weight: 600;
}
#r4w_box_tools .css-df5e0zfezt:hover{
	background:#585858;
}
#r4w_box_load_deploy_progress .css-df5e0zfezt{
    margin: auto;
    text-align: center;
}
#r4w_box_load_deploy_progress .css-df5e0zfezt a{
    background: #FA6742;
    padding: 8px;
    color: #fff;
    width: max-content;
    margin: auto;
    margin-right: 0;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
}
#r4w_box_load_deploy_progress .css-df5e0zfezt a:hover{
    background:#585858;
}
#r4w_box_tools .box_list_strategy {
    margin-top: 20px;
    margin-bottom: 20px;
}
#r4w_box_tools .css-fds5ftrg05{
    margin: auto;
    width: 100%;
    display: flex;
}
#r4w_box_tools .css-sdz5f0z{
    font-size: 20px;
    font-weight: 500;
    border-right: 1px solid #e4e4e4;
    width: 120px;
    text-align: center;
    margin: auto;
    margin-left: 15px;
    margin-right: 15px;
    border-left: 1px solid #e4e4e4;
}
#r4w_box_tools .css-sqf5eftur{
    font-size: 25px;
    font-weight: 500;
    width: 70px;
    text-align: center;
    margin: auto;
    margin-right: 15px;
}
#r4w_box_tools .css-sd5sq0dqs{
    margin: auto;
    margin-right: 0;
    height: 35px;
    width: 35px;
}
#r4w_list_strategy .r4w_show_menu{
    right:20px;
}
#r4w_box_tools .css-sdf5ef0g{
    font-weight: 700;
    width: calc(100% - 225px);
    margin: auto;
}
#r4w_box_tools .css-sdf5ef0g a{
    text-decoration:none;
    font-size: 20px;
    color: #FA6742;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    width: calc(100% - 100px);
}
#r4w_box_tools .liste_strategy{
    width: 100%;
    margin: auto;
}
#r4w_box_tools .ph-item{
    border:0;
    margin: 0;
    padding: 0;
}
#r4w_box_tools .ph-line{
    flex: inherit;
    padding: 0;
    width: 100%;
    flex-flow: initial;
}
#r4w_box_tools .ph_title{
    height: 25px;
    background-color: #ced4da;
    margin: auto;
    margin-left: 0;
    width: 100%;
}
#r4w_box_tools .ph_number{
    height: 25px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 20px;
    width: 100px;
    margin-left: 40px;
}
#r4w_box_tools .ph_percent{
    height: 25px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 20px;
    width: 80px;
    margin-left: 20px;
}
#r4w_box_tools .ph_btn{
    height: 25px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 0;
    width: 50px;
    margin-left: 0;
}
#r4w_box_load_create_str_semantic{
    min-height:250px;
    display:flex;
}
#r4w_box_load_create_str_semantic .css-df5eg0hgr{
    margin:auto;
}
#r4w_box_create_str_semantic_structure .css-sd50dfzd9,#r4w_box_duplicate_str_semantic .css-sd50dfzd9,#r4w_box_deploy_str_semantic .css-sd50dfzd9{
    display:flex;
}
#r4w_box_create_str_semantic_structure .css-df50rf8e,#r4w_box_duplicate_str_semantic .css-df50rf8e,#r4w_box_deploy_str_semantic .css-df50rf8e{
    margin: auto;
    margin-left: 0;
    width: 100%;
    text-align: right;
}
#r4w_box_create_str_semantic_structure .css-df50rf8e svg,#r4w_box_duplicate_str_semantic .css-df50rf8e svg,#r4w_box_deploy_str_semantic .css-df50rf8e svg{
    width:100px;
    height:auto;
}
#r4w_box_create_str_semantic_structure .css-tuyt8z0re,#r4w_box_duplicate_str_semantic .css-tuyt8z0re,#r4w_box_deploy_str_semantic .css-tuyt8z0re{
    margin: auto;
    width: 250px;
    display: flex;
}
#r4w_box_create_str_semantic_structure .css-re80tbv8er,#r4w_box_duplicate_str_semantic .css-re80tbv8er,#r4w_box_deploy_str_semantic .css-re80tbv8er{
    margin: auto;
    margin-right: 0;
    width: 100%;
    text-align: left;
}
#r4w_box_create_str_semantic_structure .css-re80tbv8er svg,#r4w_box_duplicate_str_semantic .css-re80tbv8er svg,#r4w_box_deploy_str_semantic .css-re80tbv8er svg{
    width:100px;
    height:auto;
}
#r4w_box_create_str_semantic_structure .r4w_wait_signals .dot,#r4w_box_duplicate_str_semantic .r4w_wait_signals .dot,#r4w_box_deploy_str_semantic .r4w_wait_signals .dot{
    width: 10px;
    height: 10px;
    background: #FA6742;
    display: inline-block;
    border-radius: 50%;
    opacity: 0;
    transform: translate(-50%, -50%);
    animation: r4w_signals_blink 0.3s ease-in infinite alternate;
}
#r4w_box_create_str_semantic_structure .r4w_wait_signals .first,#r4w_box_duplicate_str_semantic .r4w_wait_signals .first,#r4w_box_deploy_str_semantic .r4w_wait_signals .first{
    margin: auto;
    margin-left: 15px;
    animation-delay: 0.1s;
}

#r4w_box_create_str_semantic_structure .r4w_wait_signals .second,#r4w_box_duplicate_str_semantic .r4w_wait_signals .second,#r4w_box_deploy_str_semantic .r4w_wait_signals .second{
    margin-left: 30px;
    animation-delay: 0.2s;
    margin: auto;
}

#r4w_box_create_str_semantic_structure .r4w_wait_signals .third,#r4w_box_duplicate_str_semantic .r4w_wait_signals .third,#r4w_box_deploy_str_semantic .r4w_wait_signals .third{
    animation-delay: 0.3s;
    margin: auto;
    margin-right: 15px;
}
@-webkit-keyframes r4w_signals_blink{
  from { opacity: 0 }
  to { opacity: 1 }
}
@media only screen and (max-width: 500px){
  #r4w_box_create_str_semantic_structure .r4w_wait_signals,#r4w_box_duplicate_str_semantic .r4w_wait_signals,#r4w_box_deploy_str_semantic .r4w_wait_signals{
    transform: translateX(-50%) rotate(90deg);
  }
}
#r4w_box_create_str_semantic_structure .box_info,#r4w_box_duplicate_str_semantic .box_info,#r4w_box_deploy_str_semantic .box_info{
    margin-bottom: 30px;
}
#r4w_box_create_str_semantic_structure .box_info div:nth-of-type(1),#r4w_box_duplicate_str_semantic .box_info div:nth-of-type(1),#r4w_box_deploy_str_semantic .box_info div:nth-of-type(1){
    text-align: center;
    font-size: 30px;
    margin-bottom: 10px;
    font-weight: 500;
    color:#FA6742;
}
#r4w_box_create_str_semantic_structure .box_info div:nth-of-type(2),#r4w_box_duplicate_str_semantic .box_info div:nth-of-type(2),#r4w_box_deploy_str_semantic .box_info div:nth-of-type(2){
    text-align: center;
    font-size: 15px;
}
#r4w_tab_strategy_select .box_preview .css-s5df0zdgrf {
    padding: 10px;
    display: flex;
    width: 100%;
    margin: 10px;
}
#r4w_tab_strategy_select .box_preview{
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
}
#r4w_tab_strategy_select .box_preview .css-s5df0zdgrf:first-child {
    margin-left: 0;
}
#r4w_tab_strategy_select .box_preview .css-s5df0zdgrf:last-child {
    margin-right: 0;
}
#r4w_tab_strategy_select .box_preview .css-sd65z0ezf {
    width: 100%;
}
#r4w_tab_strategy_select .box_preview .sum_title {
    font-size: 14px;
    color: #adadad;
    text-align: left;
    margin-bottom: 10px;
}
#r4w_tab_strategy_select .box_preview .sum_number {
    font-size: 35px;
    font-weight: 700;
    text-align: left;
    display: contents;
}
#r4w_tab_strategy_select .box_preview .box_cnt {
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    display: flex;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
}
#r4w_tab_strategy_select .box_preview .css-sg5r0z6zfd0 .css-s5fd0eztgf {
    box-shadow: 0 1px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 4px;
    height: 53px;
    width: 53px;
    margin-left: 10px;
    display: grid;
    background-image: radial-gradient(circle at top left, #FA6742, #FA6742);
}
#r4w_tab_strategy_select .box_preview .css-sg5r0z6zfd0 .css-s5fd0eztgf svg {
    fill: #fff;
    height: 35px;
    margin: auto;
    display: table;
    min-width: 53px;
    width: auto;
}
#r4w_summary_traffic.r4w_table .cpt_num{
    background-color: #828282;
    top: -24px;
    padding: 3px;
    padding-right: 5px;
    padding-left: 5px;
    font-weight: 500;
    font-size: 13px;
}
#r4w_tab_strategy_select .sum_number svg{
    height: 22px;
    width: auto;
    fill: #585858;
}
.unavailable{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
    text-align: center;
    font-size: 20px;
    line-height:20px;
}
#r4w_list_str_semantic.box_list_str_semantic{
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    flex: 0 1 50px;
}
#r4w_list_str_semantic .box_cnt{
    width: 200px;
    margin: auto;
    margin-top: 10px;
    margin-bottom: 10px;
}
#r4w_list_str_semantic .css-fe5z0f5zez{
    width:100%;
}
#r4w_list_str_semantic .preview_image{
    cursor: pointer;
    background-image: url(../../assets/svg/preview_image.svg);
    background-repeat: no-repeat;
    background-position: center center;
    background-color: #fbfbfb;
    width: auto;
    height: 120px;
    background-size: 70px;
}
#r4w_list_deploy .list_deploy{
    width: 100%;
    margin: auto;
}
#r4w_list_deploy .ph-item{
    border:0;
    margin: 0;
    padding: 0;
}
#r4w_list_deploy.box_list_deploy{
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    flex: 0 1 50px;
}
#r4w_list_deploy .box_cnt{
    width: 100%;
    margin: auto;
    margin-top: 0px;
    margin-bottom: 10px;
}
#r4w_list_deploy .css-fe5z0f5zez{
    width:100%;
}
#r4w_list_deploy .ph_box_cnt{
     width:100%;
    display:flex;
}
#r4w_list_deploy .ph_box_left{
    width: 100%;
}
#r4w_list_deploy .ph_box_right{
    width: 51px;
}
#r4w_list_deploy .ph_title {
    height: 20px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 20px;
    width: 400px;
    margin-left: 0;
}
#r4w_list_deploy .ph_number {
     height: 15px;
     background-color: #ced4da;
     margin: auto;
     margin-right: 20px;
     width: 200px;
     margin-left: 0;
     margin-top: 10px;
}
#r4w_list_deploy .ph_timestatus {
     height: 45px;
     background-color: #ced4da;
     margin: auto;
     margin-right: 0;
     width: 100%;
     margin-left: 0;
     margin-top: 10px;
}
#r4w_list_deploy .ph_btn{
     height: 51px;
     background-color: #ced4da;
     margin: auto;
     margin-right: 0;
     width: 51px;
     margin-left: 0;
}

#r4w_box_deploy_str_semantic .css-sd5z0d8dfe{
    width: 75%;
    margin: auto;
    margin-top: 30px;
}
#r4w_box_deploy_str_semantic .css-5gferhyere5r0{
    background: #adadad;
    width: 100%;
    height: 10px;
    border-radius: 10px;
}
#r4w_box_deploy_str_semantic .css-tujr5ez0f8{
    background: #FA6742;
    width: 0%;
    height: 10px;
    border-radius: 10px;
    transition: width .4s ease-in-out;
}
#r4w_box_deploy_str_semantic .css-f5ef5g0e5rfgg{
    background: #FA6742;
    font-size: 20px;
    padding: 10px;
    width: min-content;
    margin: auto;
    border-radius: 10px;
    color: #fff;
    margin-bottom: 10px;
}
#r4w_list_str_semantic  .css-dgre5g0gre{
    display: flex;
    border-top:1px solid #d4d4d4;
    padding-top: 10px;
}
#r4w_list_str_semantic .css-d5fef0v8ef{
    width: 150px;
    padding-right: 10px;
}
#r4w_list_str_semantic .css-it8z0d8de{
    font-size: 20px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    width: calc(100% - 0px);
    height: 22px;
}
#r4w_list_str_semantic .css-it8z0d8de a{
    text-decoration:none;
    font-weight: 700;
    font-size: 20px;
    color: #FA6742;
}
#r4w_list_str_semantic .css-gr5g0rbv{
    color: #909090;
    font-size: 12px;
}
#r4w_list_str_semantic  .liste_str_semantic{
    width:100%;
}
#r4w_list_str_semantic .ph-line{
    display: initial;
}
#r4w_list_str_semantic .ph_picture {
    height: 120px;
    background-color: #ced4da;
    margin: auto;
    margin-left: 0;
    width: 100%;
    margin-bottom: 10px;
}
#r4w_list_str_semantic .ph_title {
    height: 20px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 20px;
    width: 140px;
    margin-left: 0;
}
#r4w_list_str_semantic .ph_number {
    height: 15px;
    background-color: #ced4da;
    margin: auto;
    margin-right: 20px;
    width: 100px;
    margin-left: 0;
    margin-top: 10px;
}
#r4w_list_str_semantic .ph_box_cnt{
    display:flex;
}
#r4w_list_str_semantic .ph_box_left{
    width: 160px;
}
#r4w_list_str_semantic .ph_box_right{
    width: 40px;
}
#r4w_list_str_semantic .ph_btn {
    height: 35px;
    background-color: #ced4da;
    margin: auto;
    width: 40px;
    margin-left: 0;
}
#r4w_tab_str_semantic_editor{
    height: 100%;
    display: flex;
    flex-flow: column;
}
#r4w_tab_str_semantic{
    height:100%;
}
#r4w_box_tools .css-df5dsf0effet.r4w_tab_str_semantic_title{
    padding-left: 5px;
    width: 100%;
    color: #444;
    padding: 5px;
    border: 1px solid #d4d4d4;
    box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px inset;
    font-size: 14px;
}
#r4w_box_tools .css-qxde0f8e0f {
    background: #FA6742;
    padding: 8px;
    color: #fff;
    width: max-content;
    margin: auto;
    margin-right: 0;
    cursor: pointer;
    font-weight: 600;
}
#r4w_box_tools .css-qxde0f8e0f:hover{
    background:#585858;
}
.SimpleModalJs .css-1g97g96:disabled{
    background: #bdbdbd;
    cursor: inherit;
}
#r4w_box_new_str_semantic .css-g8re0erg8e0e{
    text-align: center;
    padding: 10px;
    background: #f3f3f3;
    color: #444;
    font-weight: 500;
}
#r4w_select_strategy{
    display:none;
}
#r4w_str_structure_select .liste_strategy{
    width: 100%;
    margin: auto;
}
#r4w_str_structure_select .ph-item{
    border:0;
    margin: 0;
    padding: 0;
}
#r4w_str_structure_select .ph-line{
    flex: inherit;
    padding: 0;
    width: 100%;
    flex-flow: initial;
}
#r4w_str_structure_select .ph_select{
    height: 42px;
    background-color: #ced4da;
    margin: auto;
    width: 100%;
}
#r4w_box_load_deploy_select, #r4w_box_load_deploy_progress{
    display:none;
}
#r4w_box_load_deploy_select .deploy_info{
    display: none;
    background: #f3f3f3;
    padding: 10px;
    margin-top: -1px;
    color: #868686;
    font-weight: 500;
}
#r4w_box_load_deploy_select .deploy_info .css-d5zd0zg{
    display:flex;
}
#r4w_box_load_deploy_select .deploy_info .css-d5zd0zg div{
    margin:auto;
    margin-left:0;
    margin-right:0;
}
#r4w_box_load_deploy_select .deploy_info svg{
    width: 20px;
    height: auto;
    fill: #dc3232;
}
#r4w_box_load_deploy_select .deploy_info div.css-fef5ez0f{
    margin-right: 10px;
}
#r4w_box_deploy_str_semantic #r4w_finish{
    display:none
}
#r4w_list_str_semantic .css-d5sd0s5d{
    background: #FA6742;
    height: 25px;
    width: 25px;
    position: absolute;
    padding: 5px;
    border-radius: 10px;
    display: flow-root;
    margin: auto;
    margin-top: -20px;
    margin-left: -20px;
}
#r4w_list_str_semantic .css-d5sd0s5d svg{
    height: 100%;
    width: auto;
    fill: #fff;
}
#r4w_tab_str_semantic .r4w_bullet_info{
    background: #ffffff;
}
#r4w_tab_str_semantic .r4w_bullet_info svg{
    height: 30px;
    width: auto;
    fill: #FA6742;
    margin: auto;
}
#r4w_list_str_semantic .box_cnt.protected{
    background: #c7c7c7;
}
#r4w_list_str_semantic .box_cnt.protected .preview_image{
    opacity:0.7;
}
#r4w_list_str_semantic .box_cnt.protected .css-it8z0d8de a,#r4w_list_str_semantic .box_cnt.protected .css-gr5g0rbv{
    color: #676767;
}
#r4w_list_str_semantic .css-sd6e0fe5fe{
    height: 30px;
    width: 30px;
    position: absolute;
    padding: 5px;
    border-radius: 10px;
    display: flow-root;
    margin: auto;
    margin-top: -25px;
    margin-left: -25px;
    z-index: 300;
}
#r4w_list_str_semantic .css-sd6e0fe5fe svg{
    height: 100%;
    width: auto;
    fill: #444;
}
#r4w_msgsync_str_semantic .css-df5e0fe5f svg{
    height: 30px;
    width: auto;
    fill: #F44336;
    margin: auto;
}
#r4w_msgsync_str_semantic .r4w_bullet_info .css-df5r0grg{
    margin: auto;
    margin-left: 5px;
}
#r4w_box_load_limit_deploy .css-s5dsq0d5qsd{
    display: flex;
    margin-top: 20px;
    flex-flow: wrap;
}
#r4w_box_load_limit_deploy .css-sdfsd20fs{
    cursor:pointer;
    width: 100%;
    margin: auto;
    margin-top: 10px;
    margin-bottom: 10px;
    border-radius: 15px;
    color: #fff;
    padding: 15px;
    display:flex;
}
#r4w_box_load_limit_deploy .css-fd5sdg0sdf5{
    font-size: 25px;
    font-weight: 500;
}
#r4w_box_load_limit_deploy .css-sqf50gfeg5e{
    font-size: 13px;
    margin-top: 10px;
}
#r4w_box_load_limit_deploy .css-dsd50dz{
    background: #f88f5f;
}
#r4w_box_load_limit_deploy .css-g3zse0f{
    background: #9E9E9E;
}
#r4w_box_load_limit_deploy .css-s5df0sfr{
    background:#ffbf00;
}
#r4w_box_load_limit_deploy .css-df2dsfef0{
    background: #00BCD4;
}
#r4w_box_load_limit_deploy .css-sdfsd20fs:hover{
    background: #585858;
}
#r4w_box_load_limit_deploy .css-sdf5eg0e{
    margin: auto;
    margin-right: 15px;
    margin-left: 0;
}
#r4w_box_load_limit_deploy .css-sdf5eg0e svg{
    height: 50px;
    width: auto;
    fill:#fff;
}
#r4w_box_load_limit_deploy .css-dsf1df0sd{
    font-size: 15px;
    text-align: center;
    color: #444;
}
#r4w_box_load_limit_deploy .css-sdfsd20fs a{
    color: #fff;
    text-decoration: none;
    display: flex;
}
#r4w_box_load_limit_deploy #r4w_nd_waiting,#r4w_box_load_limit_deploy #r4w_nd_subscribe{
    display:none;
}


#r4w_box_stripe input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}
#r4w_box_stripe input[type=number]::-webkit-inner-spin-button,
#r4w_box_stripe input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}
#r4w_box_stripe .number-input {
    margin: auto;
    position: relative;
    width: max-content;
    display:flex;
    border: 1px solid #444;
}
#r4w_box_stripe .number-input,
#r4w_box_stripe .number-input * {
  box-sizing: border-box;
}
#r4w_box_stripe .number-input button {
    outline: none;
    -webkit-appearance: none;
    background-color: #444444;
    border: none;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    margin: 0;
    position: relative;
}
#r4w_box_stripe .number-input button:before,
#r4w_box_stripe .number-input button:after {
display: inline-block;
    position: absolute;
    content: '';
    width: 15px;
    height: 2px;
    background-color: #ffffff;
    transform: translate(-50%, -50%);
}
#r4w_box_stripe .number-input button.plus:after {
  transform: translate(-50%, -50%) rotate(90deg);
}
#r4w_box_stripe .number-input input[type=number] {
    max-width: 50px;
    padding: 5px;
    border: 0;
    font-size: 20px;
    height: 25px;
    font-weight: bold;
    text-align: center;
    margin: auto;
    box-shadow:none;
    background: #fff;
}
#r4w_box_stripe .number-input{
    margin-top: 15px;
    margin-bottom: 15px;
}
#r4w_box_stripe .css-d2sd0sdd{
    padding: 15px;
    margin: 0;
}
#r4w_box_stripe .css-sd5sd0qsdq{
    font-size: 12px;
    color: #9E9E9E;
}
#r4w_box_stripe .price small{
    font-size: 15px;
}
#r4w_box_wait .css-5f0zeg5fe6fze{
    display:block;
    margin-top: 32px;
    margin-bottom: 32px;
}
#r4w_deploy_being svg{
    height: 50px;
    width: auto;
    margin: auto;
    display: flex;
    fill: #FA6742;
}
#r4w_box_deploy_being .btn_stop{
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
    margin-top:20px;
}
#r4w_box_deploy_being .btn_stop:hover{
    background: #444444;
}
#r4w_tab_deploy .css-fd5s5f54ss6{
     box-shadow: 0px 3px 7px 0 rgba(148, 148, 148, 0.3);
     border-radius: 5px;
}
#r4w_tab_deploy .css-sf5ee8z8ezg{
     padding: 10px 10px;
     background: #ffffff;
     display: flex;
     margin-top: 20px;
     height: auto;
     min-height: 35px;
     border-top-left-radius: 5px;
     border-top-right-radius: 5px;
     border-bottom-right-radius: 0;
     border-bottom-left-radius: 0;
}
#r4w_tab_deploy .css-g5regg8r2r{
     padding: 10px 10px;
     background: #ccc;
     display: flex;
     height: auto;
     min-height: 30px;
     border-top-left-radius: 0;
     border-top-right-radius: 0;
     border-bottom-right-radius: 5px;
     border-bottom-left-radius: 5px;
}
#r4w_tab_deploy .css-gkktth89y2d{
     display: flex;
     margin: auto;
     margin-left: 0;
}
#r4w_tab_deploy .css-f8egeg89e{
     display: flex;
     margin-right:20px;
}
#r4w_tab_deploy .css-5fe5f2gee{
     display: flex;
}
#r4w_tab_deploy .css-f6efe5f2e{
     display: flex;
     margin: auto;
     margin-right: 0;
}
#r4w_tab_deploy .css-55e2eg82rg{
     font-weight: 500;
     color: #72777c;
     margin:auto;
     margin-right: 5px;
}
#r4w_tab_deploy .css-5d5f5gr8gr5{
     font-weight: 500;
     display:flex;
}
#r4w_tab_deploy .css-5f8h6regg{
     font-size: 25px;
     font-weight: 500;
     margin-bottom: 5px;
}
#r4w_tab_deploy .css-5fgr9h4rh6{
     font-size: 14px;
     font-weight: 500;
}
#r4w_tab_deploy .css-6fd54f6sd{
     margin: auto;
     margin-left: 0;
}
#r4w_tab_deploy .css-f6eg5e6fef{
     margin: auto;
     margin-right: 0;
}
#r4w_tab_deploy .css-df565d2gfe{
     background: #cccccc;
     height: 50px;
     width: 50px;
     border-radius: 50px;
     border: 1px dashed #72777c;
}
#r4w_tab_deploy .css-5s5fsdg1gd svg{
     height: 30px;
     width: auto;
     padding: 10.5px;
     fill: #72777c;
}
#r4w_tab_deploy .css-5s5fsdg1gd svg.css-5e0fe8ef5e2e{
     padding-top: 8px;
}
#r4w_tab_deploy .css-gg5s56f29e2f{
     display:flex;
     margin:auto;
}
#r4w_tab_deploy .ghl_deploy_progress .css-g5regg8r2r{
     background: #eaeffb;
}
#r4w_tab_deploy .ghl_deploy_done .css-g5regg8r2r{
     background: #e1f8f5;
}
#r4w_tab_deploy .ghl_deploy_warning .css-g5regg8r2r{
     background: #fdebdd;
}
#r4w_tab_deploy .ghl_deploy_progress .css-5s5fsdg1gd svg{
     fill:#92a3d9;
}
#r4w_tab_deploy .ghl_deploy_done .css-5s5fsdg1gd svg{
    fill: #22d6b3;
}
#r4w_tab_deploy .ghl_deploy_warning .css-5s5fsdg1gd svg{
     fill:#ff6f02;
}
#r4w_tab_deploy .ghl_deploy_progress .css-df565d2gfe{
     background: #eaeffb;
     border: 1px dashed #89a2d9;
}
#r4w_tab_deploy .ghl_deploy_done .css-df565d2gfe{
     background: #e1f8f5;
     border: 1px dashed #22d6b3;
}
#r4w_tab_deploy .ghl_deploy_warning .css-df565d2gfe{
    background: #fdebdd;
    border: 1px dashed #ff6f02;
}
#r4w_tab_deploy .ghl_deploy_progress .css-55e2eg82rg{
    color: #92a3d9
}
#r4w_tab_deploy .ghl_deploy_done .css-55e2eg82rg{
     color: #22d6b3;
}
#r4w_tab_deploy .ghl_deploy_warning .css-55e2eg82rg{
     color: #ff6f02;
}
#r4w_tab_deploy .css-sdf62rrgge{
     margin:auto;
}
#r4w_tab_deploy .ghl_deploy_warning .css-2fe5e2ezzede{
     cursor:pointer;
     background: #ff6f02;
     color: #fff;
     border-radius: 5px;
     padding: 5px;
     padding-left: 10px;
     padding-right: 10px;
     margin: auto;
     margin-left: 10px;
     margin-right: 10px;
}
.css-sd5sd5s2d label{
     display: block;
     font-size: 14px;
     font-weight: 400;
     color: #FA6742;
     margin: 0.5rem 0px;
     cursor: default;
     margin-bottom: 5px;
}
.css-sd5sd5s2d .css-5fe5fe52fe{
     font-size: 20px;
}
#r4w_box_deploy_str_semantic .css-g50rdsg5feg{
     background: #d6d6d6;
     padding: 15px;
     margin-top: -1px;
     color: #868686;
     font-weight: 500;
     border-top-right-radius: 10px;
     border-bottom-left-radius: 10px;
}
#r4w_box_deploy_str_semantic .css-sd20zdfefe{
     display: flex;
     margin-bottom: 10px;
     margin-top: 10px;
}
#r4w_box_deploy_str_semantic .css-sd20zdfefe:first-child{
     margin-top:0;
}
#r4w_box_deploy_str_semantic .css-sd20zdfefe:last-child{
     margin-bottom:0;
}
#r4w_box_deploy_str_semantic .css-f50ef5ege{
     margin: auto;
     margin-left: 0;
}
#r4w_box_deploy_str_semantic .css-f5e0g5egt{
     margin: auto;
     margin-right: 0;
}
#r4w_box_deploy_str_semantic #r4w_deploy_price{
     font-size:22px;
     color:#FA6742;
}
#r4w_box_deploy_str_semantic #box_prview_order{
     background: #f3f3f3;
     padding: 15px;
     margin-top: 15px;
     padding-top: 5px;
}
#r4w_box_deploy_str_semantic #box_prview_order.css-sd5sd5s2d label{
     font-size: 15px;
     font-weight: 500;
}
#r4w_box_stripe_preview .css-f5d0ef5efef{
     font-size: 30px;
     text-align: center;
     margin-top: 10px;
     margin-bottom: 20px;
     color: #FA6742;
}
#r4w_tab_deploy .css-3134a1f79f1e{
    text-decoration: none;
}