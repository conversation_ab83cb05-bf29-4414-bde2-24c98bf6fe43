<div id="r4w_system_message"></div>
<div id="r4w_auth_register">
  <div class="css-100frqb">
      <div class="css-1k787id">
          <div class="css-gmuwbf">
              <div class="css-9388ij">
                  <div class="css-gzb9kt">
                      <div class="css-1rvpz4w">
                        [@svg_rank4win_black]
                      </div>
                      <a class="css-1rvpz4w" href="[@url_login]">
                        {_('Already have an account')} ?
                        <span font-weight="bold" color="#5A4FBD" class="css-6iidyz e178wkxy0">{_('Login here')}.</span>
                      </a>
                  </div>
                  <div class="css-8atqhb">
                    <div id="r4w_error"></div>
                    <div name="email" class="css-4ywf4y">
                        <label for="user" class="css-ya34dr">{_('Email Address')}</label>
                        <div>
                            <input id="user" name="user" type="email" placeholder="[@placeholder_email]" class="css-1rkhnpn r4wfix_wp_input">
                        </div>
                    </div>
                    <div name="password" type="password" class="css-17maygl">
                        <label for="pwd" class="css-ya34dr">{_('Password')}</label>
                        <div>
                            <input id="pwd" name="pwd" placeholder="[@placeholder_password]" type="password" class="css-1rkhnpn r4wfix_wp_input" aria-autocomplete="list" autocomplete="new-password">
                        </div>
                    </div>
                    <div name="confirm" type="password" class="css-17maygl">
                        <label for="repwd" class="css-ya34dr">{_('Confirm Password')}</label>
                        <div>
                            <input id="repwd" name="repwd" placeholder="[@placeholder_confirm_password]" type="password" class="css-1rkhnpn r4wfix_wp_input">
                        </div>
                    </div>
                    <div id="loading"><div class="dual-ring"></div></div>
                    <button id="btn_submit" class="css-1g97g96">{_('Create Account')}</button>
                  </div>
                  <div class="css-1yuhvjn">
                    <a target="_blanc" href="[@r4w_url_privacy]">
                      {_('Terms of Service')}
                      &amp;
                      {_('Privacy Policy')}
                    </a>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>