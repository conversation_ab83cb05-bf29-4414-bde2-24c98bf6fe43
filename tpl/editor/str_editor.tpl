<div id="ph_str_editor" class="ph_box"><div class="ph-item"></div></div>
<div id="r4w_tab_str_semantic_editor">
	<div id="rank4win-cloud-editor" data-diagram="[@editor_diagram]" ng-app="cloud-editor" data-current-diagram="" wp-admin-url-page="[@wp_admin_url_page]"  ng-cntroller="MainController">
	    <!-- Online Mode: AngularJS Editor Component -->
	    <rank4win-editor></rank4win-editor>
	    
	    <!-- Offline Mode: Placeholder for JavaScript-Generated Editor -->
	    <div id="offline-editor-placeholder" style="display: none;">
	        <div class="offline-mode-notice">
	            <h3>🔧 Offline Structure Editor</h3>
	            <p>Working in offline mode. Some features may be limited.</p>
	        </div>
	        <!-- Content will be dynamically generated by offline-editor.js -->
	    </div>
	</div>
	<div class="box_save">
		<div class="save_display save_protected">
			<div class="save_svg"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 347.971 347.971" xml:space="preserve"><path d="M317.309,54.367C257.933,54.367,212.445,37.403,173.98,0C135.519,37.403,90.033,54.367,30.662,54.367 c0,97.405-20.155,236.937,143.317,293.604C337.463,291.305,317.309,151.773,317.309,54.367z M162.107,225.773l-47.749-47.756 l21.379-21.378l26.37,26.376l50.121-50.122l21.378,21.378L162.107,225.773z"></path></svg></div>
			<div class="save_txt">{_('Protected mode')}</div>
		</div>
		<div class="save_display save_saved">
			<div class="save_svg"><svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130.2 130.2">  <circle class="svg_mark_crosss_path svg_mark_crosss_circle" fill="none" stroke="#7ac142" stroke-width="6" stroke-miterlimit="10" cx="65.1" cy="65.1" r="62.1"/><polyline class="svg_mark_crosss_path svg_mark_crosss_check" fill="none" stroke="#7ac142" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" points="100.2,40.2 51.5,88.8 29.8,67.5 "/></svg></div>
			<div class="save_txt">{_('Saved structure')}</div>
		</div>
		<div class="save_display save_progress">
			<div class="save_svg"><svg class="circular-loader"viewBox="25 25 50 50" ><circle class="loader-path" cx="50" cy="50" r="20" fill="none" stroke="#70c542" stroke-width="2" /></svg></div>
			<div class="save_txt">{_('Backup in progress')}</div>
		</div>
		<div class="save_display save_error">
			<div class="save_svg"><svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130.2 130.2"><circle class="svg_mark_crosss_path svg_mark_crosss_circle" fill="none" stroke="#D06079" stroke-width="6" stroke-miterlimit="10" cx="65.1" cy="65.1" r="62.1"/><line class="svg_mark_crosss_path svg_mark_crosss_line" fill="none" stroke="#D06079" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" x1="34.4" y1="37.9" x2="95.8" y2="92.3"/><line class="svg_mark_crosss_path svg_mark_crosss_line" fill="none" stroke="#D06079" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" x1="95.8" y1="38" x2="34.4" y2="92.2"/></svg></div>
			<div class="save_txt">{_('Unsaved structure')}</div>
		</div>
	</div>
</div>