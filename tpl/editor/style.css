.admin_page_r4w_editor #wpbody,.admin_page_r4w_editor #wpbody-content,#r4w_box_editor,#r4w_box_editor .tab_container,#r4w_box_editor .wrap{
    height:100%;
    position: relative;
    display: flex;
    flex-flow: column;
    font-family: system-ui;
}
#wpbody-content {
    padding-bottom: 20px;
}
.admin_page_r4w_editor #wpcontent,.admin_page_r4w_editor #wpfooter{
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
}
#r4w_box_editor .r4w_splitter-container{
    height: calc(100% - 20px);
}
#rank4win-cloud-editor *{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
#r4w_box_editor .r4w_splitter-container .splitter {
    margin-bottom: 25px;
    margin-top: 70px;
    border-radius: 5px;
	box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
}
.admin_page_r4w_editor .r4w_msg_opr_maintenance {
    position: relative;
    display: flex;
    margin: 10px 20px 20px 2px;
}
#r4w_box_editor .r4w_splitter-right{
    margin-top: 70px;
    margin-bottom: 25px;
}
#r4w_box_editor .css-sfd5e0fze{
    margin-left: 15px;
    margin-right: 6px;
    background: #fff!important;
    border-radius: 10px;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
}
#r4w_box_editor .css-sf50ezf{
    height: 100%;
    position: relative;
    display: flex;
    overflow-x: hidden;
}
#r4w_box_editor .css-f5zf0ef0z{
    height: inherit;
    width: 100%;
    max-height: 100%;
    overflow-x: hidden;
}
#r4w_box_editor .kic_box_search {
    background-color: #2f2c2c;
    margin-bottom: 20px;
    padding: 10px;
    border-radius: 3px;
    margin: auto;
    display: flex;
}
#r4w_box_editor .kic_input_search {
    display: flex;
    background-color: #434343;
    -webkit-box-align: center;
    align-items: center;
    flex-grow: 1;
    border-radius: 2px;
    margin-right: 20px;

}
#r4w_box_editor .kic_input_search input {
    width: 100%;
    background-color: transparent;
    color: white;
    font-size: 15px;
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    box-sizing: border-box;
    outline: 0;
    box-shadow: inset 0 0px 0px;
}
#r4w_box_editor .kic_search_btn {
    background-color: #FA6742;
    font-size: 25px;
    width: 50px;
    cursor: pointer;
    color: #fff;
    letter-spacing: 2px;
    height: 40px;
    border-radius: 5px;
    margin: 0;
    padding: 0;
}
#r4w_box_editor .kic_search_btn svg {
    margin: auto;
    display: flex;
    top: 8px;
    position: relative;
}
#r4w_box_editor .kic_search_btn:hover{
    background: #434343;
}
#r4w_box_editor .kic_search_btn:hover svg{
    fill: #fff;
}
#r4w_box_editor input:-webkit-autofill,
#r4w_box_editor input:-webkit-autofill:hover,
#r4w_box_editor input:-webkit-autofill:focus,
#r4w_box_editor textarea:-webkit-autofill,
#r4w_box_editor textarea:-webkit-autofill:hover,
#r4w_box_editor textarea:-webkit-autofill:focus,
#r4w_box_editor select:-webkit-autofill,
#r4w_box_editor select:-webkit-autofill:hover,
#r4w_box_editor select:-webkit-autofill:focus {
    border: 0;
    -webkit-text-fill-color: #fff;
    -webkit-box-shadow: 0 0 0px 21px #434343 inset;
    transition: background-color 5000s ease-in-out 0s;
    background-color: rgb(67, 67, 67) !important;
    background-image: none !important;
    border-radius: 0;
}
#r4w_box_editor .r4w_splitter-left{
    width: 100%;
}
#r4w_box_editor .r4w_splitter-right{
    width:100%;
    height: auto;
    position: relative;
}
#r4w_box_editor .r4w_splitter-right .r4w_right_limit{
    min-width: 370px;
    width:100%;
    height: 100%;
    display: flex;
    flex-flow: column;
}
#r4w_box_editor ul#r4w-tabs{
    margin-left:15px;
    margin-right: 6px;
}
#r4w_box_editor ul#r4w-tabs li{
    padding:10px;
    margin:auto;
}
#r4w_box_editor .ph-item{
    border:0;
    margin: 0;
    padding: 0;
}
#r4w_box_editor .ph-line{
    flex: inherit;
    flex-flow: inherit;
    padding: 0;
    width: 100%;
}
#r4w_box_editor .ph_advbox{
    height: 34px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-bottom:10px;
}
#r4w_box_editor .ph_advbox:last-child{
    margin:auto;
}
#r4w_box_editor .r4w_table .cpt_num{
    font-size: 12px;
    font-weight: 500;
    top: -17px;
}
#r4w_box_editor .r4w_bullet_info{
    margin-top: 20px;
}
#r4w_box_editor .r4w_splitter-right .simplebar-scrollbar:before{
    background: #FA6742;
}
#r4w_box_editor .r4w_splitter-right .tab-pane, #r4w_box_editor .ph-item{
    padding:10px;
}
#r4w_box_editor .save_editor {
    border-radius: 4px
}
#r4w_box_editor.r4w_editor_save_success .save_display.save_saved {
    display: block
}
#r4w_box_editor.r4w_editor_save_progress .save_display.save_progress {
    display: block
}
#r4w_box_editor.r4w_editor_save_protected .save_display.save_protected svg {
    fill: #767676;
}
#r4w_box_editor.r4w_editor_save_protected .save_display.save_protected {
    display: block
}
#r4w_box_editor.r4w_editor_save_error .save_display.save_error {
    display: block
}
#r4w_box_editor .save_display {
    display: none
}
@media(max-width:767px) {
    #r4w_box_editor .save_display {
        width: 100%
    }
}
#r4w_box_editor .box_save{
    margin-top: -50px;
    z-index: 1040;
    float: right;
    position: relative;
    margin-right: 20px;
}
#r4w_box_editor .box_saving {
    height: 40px;
}
#r4w_box_editor .save_display .save_svg {
    float: right;
    margin-left: 10px
}
#r4w_box_editor .save_display .save_txt {
    line-height: 27px;
    font-size: 12px;
    text-transform: uppercase;
    float: right;
    font-weight:500;
    animation: fadein 2s;
    -moz-animation: fadein 2s;
    -webkit-animation: fadein 2s;
    -o-animation: fadein 2s
}
#r4w_box_editor.r4w_editor_save_success .save_display .save_txt {
    color: #7ac142
}
#r4w_box_editor.r4w_editor_save_progress .save_display .save_txt {
    color: #767676
}
#r4w_box_editor.r4w_editor_save_protected .save_display .save_txt {
    color: #767676
}
#r4w_box_editor.r4w_editor_save_error .save_display .save_txt {
    color: #d06079
}
#r4w_box_editor .save_svg svg {
    height: 25px;
    width: 25px
}
#r4w_box_editor .svg_mark_crosss_path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0
}
#r4w_box_editor .svg_mark_crosss_circle {
    -webkit-animation: svg_mark_crosss_dash .9s ease-in-out;
    animation: svg_mark_crosss_dash .9s ease-in-out
}
#r4w_box_editor .svg_mark_crosss_line {
    stroke-dashoffset: 1000;
    -webkit-animation: svg_mark_crosss_dash .9s .35s ease-in-out forwards;
    animation: svg_mark_crosss_dash .9s .35s ease-in-out forwards
}
#r4w_box_editor .svg_mark_crosss_check {
    stroke-dashoffset: -100;
    -webkit-animation: svg_mark_crosss_dash-check .9s .35s ease-in-out forwards;
    animation: svg_mark_crosss_dash-check .9s .35s ease-in-out forwards
}
@-webkit-keyframes svg_mark_crosss_dash {
    0% {
        stroke-dashoffset: 1000
    }
    100% {
        stroke-dashoffset: 0
    }
}
@keyframes svg_mark_crosss_dash {
    0% {
        stroke-dashoffset: 1000
    }
    100% {
        stroke-dashoffset: 0
    }
}
@-webkit-keyframes svg_mark_crosss_dash-check {
    0% {
        stroke-dashoffset: -100
    }
    100% {
        stroke-dashoffset: 900
    }
}
@keyframes svg_mark_crosss_dash-check {
    0% {
        stroke-dashoffset: -100
    }
    100% {
        stroke-dashoffset: 900
    }
}
#r4w_box_editor .circular-loader {
    height: 25px;
    width: 25px;
    -webkit-animation: rotate 2s linear infinite;
    animation: rotate 2s linear infinite;
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center
}
#r4w_box_editor .loader-path {
    stroke-dasharray: 150, 200;
    stroke-dashoffset: -10;
    -webkit-animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
    animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
    stroke-linecap: round
}
@-webkit-keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}
@keyframes rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}
@-webkit-keyframes dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124
    }
}
@keyframes dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124
    }
}
@-webkit-keyframes color {
    0% {
        stroke: #767676
    }
    40% {
        stroke: #767676
    }
    66% {
        stroke: #767676
    }
    80%,
    90% {
        stroke: #767676
    }
}
@keyframes color {
    0% {
        stroke: #767676
    }
    40% {
        stroke: #767676
    }
    66% {
        stroke: #767676
    }
    80%,
    90% {
        stroke: #767676
    }
}
@keyframes fadein {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}
@-moz-keyframes fadein {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}
@-webkit-keyframes fadein {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}
@-o-keyframes fadein {
    from {
        opacity: 0
    }
    to {
        opacity: 1
    }
}
#r4w_box_editor #r4w_tab_search_answer .arrow_more {
    margin-right: 8px;
    -webkit-transition: -webkit-transform .3s ease;
    transition: -webkit-transform .3s ease;
    -o-transition: transform .3s ease;
    transition: transform .3s ease;
    transition: transform .3s ease,-webkit-transform .3s ease;
}
#r4w_box_editor #r4w_tab_search_answer .rotate-z {
    -webkit-transform: rotateZ(90deg);
    -ms-transform: rotate(90deg);
    transform: rotateZ(90deg);
}
#r4w_box_editor #r4w_tab_search_answer img.arrow_more {
    height: 9px;
    margin-top: 5px;
}
#r4w_box_editor #r4w_tab_search_answer .list_document .js-diary-show-details {
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    border-bottom: 1px solid #ededed;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}
#r4w_box_editor #r4w_tab_search_answer .list_document {
    border-top: 0;
    background: #edeff2;
    margin-bottom: 10px;
}
#r4w_box_editor #r4w_tab_search_answer .col_7mWSf {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: wrap;
    flex-flow: wrap;
    -ms-flex-align: center;
    align-items: center;
}
#r4w_box_editor #r4w_tab_search_answer .list_document_answers{
    display:none;
}
#r4w_box_editor #r4w_tab_search_answer .list_document_answers div:nth-child(2n) {
    background-color: #fbfcfe;
}
#r4w_box_editor #r4w_tab_search_answer .list_document_answers div:nth-child(2n+1) {
    background-color: #f5f6f7;
}
#r4w_box_editor #r4w_tab_search_answer .list_document_answers div {
    padding: 10px;
    font-weight: 400;
}
#r4w_box_editor #r4w_tab_search_answer .idea_content{
    margin-right:15px;
}
#r4w_box_editor #r4w_tab_search_answer .inc_topic{
    cursor:pointer;
}
#r4w_box_editor #r4w_tab_search_answer .search_content{
    padding: 10px;
    overflow-x: hidden;
}
#r4w_box_editor .css-f5e0f85ef{
    padding: 20px;
    font-size: 15px;
    text-align: center;
    color: #FA6742;
    font-weight: 500;
}
#r4w_box_editor #r4w_tab_search_answer .css-f5e0f85ef{
    padding:10px;
}
#r4w_box_used_str_semantic .css-sf5s0fff{
    display:flex;
}
#r4w_box_used_str_semantic.SimpleModalJs .css-s5d0sqdf{
    background-color: #9E9E9E;
}
#r4w_box_used_str_semantic.SimpleModalJs .css-s5ff0etge{
    background-color: #FF5722;
}
#r4w_box_used_str_semantic.SimpleModalJs  .css-1g97g96{
    display: inline-flex;
    -webkit-font-smoothing: antialiased;
    vertical-align: middle;
    text-align: center;
    -webkit-appearance: none;
    cursor: pointer;
    font-family: inherit;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    font-size: 14px;
    outline: none;
    text-decoration: none;
    transition: all 0.2s ease-in-out 0s;
    border-width: 0px;
    border-radius: 4px;
    border-color: #0c2f55;
    padding: 16px;
    width: 40%;
    margin: auto;
}
#r4w_box_used_str_semantic.SimpleModalJs .css-1g97g96:hover {
    background: #2f2c2c;
}
#r4w_box_editor .r4w_splitter-right .ph_box{
    margin-right: 6px;
    margin-left: 15px;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph-item{
    margin-bottom: 20px;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph_search{
    display:flex;
    width:100%;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph_input{
    background-color: #ced4da;
    margin: auto;
    height: 40px;
    margin-left: 0;
    width: 100%;
    margin-right: 20px;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph_search .ph_btn{
    background-color: #ced4da;
    width: 50px;
    margin: auto;
    height: 40px;
    margin-right: 0;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph_li{
    display:flex;
    width:100%;
    padding-top: 10px;
    padding-bottom: 10px;
}
#r4w_box_editor .r4w_splitter-right .ph_box .ph_li .ph_btn{
    background-color: #ced4da;
    width: 90px;
    margin: auto;
    height: 38px;
}
#r4w_box_editor .r4w_splitter-left .ph_box{
    position: relative;
    min-height: 350px;
    height: calc(100% - 0px);
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    padding-bottom: 15px;
    padding-top: 60px;
}
#r4w_box_editor .r4w_splitter-left .ph_box .ph-item{
    height: 100%;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 10px;
}
#r4w_msgprotected_str_semantic .r4w_bullet_info{
    margin: 10px 30px 0px 10px;
    background: #fbfbfb;
}
#rank4win-cloud-editor .css-sf2e5fezf0{
    position: absolute;
    right: 0;
}
#rank4win-cloud-editor .css-sf2e5fezf0 a{
    color: #fff;
    background: #FA6742;
}
#r4w_box_wait .css-5f0zeg5fe6fze{
    display:block;
    margin-top: 32px;
    margin-bottom: 32px;
}
#r4w_editor_right .css-5fe05f5z0f{
    font-size: 20px;
}
#r4w_editor_right .css-ferg5r0hg{
    font-size: 13px;
    margin-top: 10px;
}
#r4w_editor_right .css-5f0efeg5eg{
    text-align: center;
    padding: 10px;
}
#r4w_editor_right .css-5f0efeg5eg svg{
    width: 40px;
    fill: #434343;
}
#r4w_editor_right .css-sdf5e0f5ezfz{
    margin-top: 15px;
}
#r4w_editor_right a.css-sd520sqd5z {
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_editor_right a.css-sd520sqd5z:hover {
    background: #444444;
}
#r4w_editor_right .blurring{
    color: transparent;
    text-shadow: 0 0 11px rgba(0,0,0,0.5);
    transition: all 0.5s;
    -webkit-user-select: none; /* Chrome all / Safari all */
    -moz-user-select: none;    /* Firefox all             */
    -ms-user-select: none;     /* IE 10+                  */
     user-select: none;        /* Likely future           */
}
#r4w_editor_right .css-5fe05f5z0f{
    font-size: 20px;
}
#r4w_editor_right .css-ferg5r0hg{
    font-size: 13px;
    margin-top: 10px;
}
#r4w_editor_right .css-d50f65efe{
    background: #ffffffb0;
    margin: auto;
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translate(-50%, 0);
    max-width: 470px;
}
#r4w_editor_right .css-5f0efeg5eg{
    text-align: center;
    padding: 15px;
}
#r4w_editor_right .css-5f0efeg5eg svg{
    width: 40px;
    fill: #434343;
}
#r4w_editor_right .css-sdf5e0f5ezfz{
    margin-top: 15px;
}
#r4w_editor_right a.css-sd520sqd5z {
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_editor_right a.css-sd520sqd5z:hover {
    background: #444444;
}
#r4w_editor_right .css-f5e0f5ezfze{
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    border-bottom: 1px solid #ededed;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}
#r4w_deploy_being svg{
    height: 50px;
    width: auto;
    margin: auto;
    display: flex;
    fill: #FA6742;
}
#r4w_box_deploy_being .btn_stop{
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
    margin-top:20px;
}
#r4w_box_deploy_being .btn_stop:hover{
    background: #444444;
}
#r4w_box_editor .r4w_vbeta{
    z-index: 100;
    font-size: 13px;
    position: absolute;
    color: #ffffff;
    font-weight: 500;
    text-transform: uppercase;
    padding: 5px;
    background: #ffdbd2;
    border-radius: 5px;
    margin: 5px;
    opacity: 1;
}
#r4w_editor_right .css-f5e0f5zef0{
    top: 310px;
}
#r4w_editor_right .css-f5gr50hykihj{
     font-size: 20px;
     font-weight: bold;
     color: #FA6742;
     margin-bottom: 10px;
}
#r4w_editor_right .css-5g5hjt0i5ykk{
     border-color: #757575;
     background: linear-gradient(to right, #757575 50%, #757575 50%);
     background-repeat: no-repeat;
     box-sizing: border-box;
     display: -ms-inline-flexbox;
     display: inline-flex;
     margin: 5px 5px 0 0;
     max-width: 100%;
     border-radius: 10px;
     padding:3px;
     -ms-flex-align: center;
     align-items: center;
     line-height: 18px;
     oveflow: hidden;
     -o-text-overflow: ellipsis;
     -o-transition: all .3s ease;
}
.css-77fe49a2eba1{
    text-transform: uppercase;
    font-size: 15px;
}
.css-b4b5877b6435{
    margin-left: 5px;
    display: flex;
}
#r4w_editor_right .css-f50g5egrthyr{
    padding-left: 5px;
    padding-right: 5px;
    color: #fff;
    margin-bottom:0.5px;
}
#r4w_editor_right .keyword_solo {
    color: #323030;
    background: none;
    font-size: 12px;
    font-weight: 500;
}
#r4w_editor_right .css-d5f0d5gdghh{
     display: flex;
     margin-top: 10px;
     margin-bottom: 10px;
}
#r4w_editor_right .css-d5f0d5gdghh a{
     background: #FA6742;
     padding: 8px;
     color: #fff;
     width: max-content;
     margin: auto;
     cursor: pointer;
     font-weight: 600;
     text-decoration:none;
}
#r4w_editor_right .css-dg50dg5egvb{
     height: 10px;
     width: 15px;
     margin: auto;
     margin-right: 0;
     background: #585858;
     border-radius: 10px;
}
#r4w_editor_right .r4wPage_darft{
     background: #F44336;
}
#r4w_editor_right .r4wPage_publish{
     background: #4CAF50;
}
#r4w_editor_page_info{
    margin-left: 15px;
    margin-right: -1px;
    position: absolute;
    z-index: 200;
    height: 100%;
    width: 100%;
    background: #44444497;
    width: calc(100% - 21px);
    border-radius: 10px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
#r4w_editor_page_info .css-bbf4caf1f133{
    height: 100%;
}
#r4w_editor_page_info .css-3c61c15b8f61{
    position: absolute;
    right: 11px;
    top: -38px;
    background: #F44336;
    border-radius: 5px;
    width: fit-content;
    display: flex;
    cursor: pointer;
    color: #fff;
    margin: auto;
    font-size: 22px;
    padding: 10px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
#r4w_editor_page_info .css-3c61c15b8f61{
}
#r4w_editor_page_info .css-3c61c15b8f61{
    margin-left: 5px;
    display: flex;
}
#r4w_editor_page_info .css-3c61c15b8f61 svg{
    height: 10px;
    width: auto;
    fill: #fff;
    margin: auto;
}
#r4w_editor_search{
    display: contents;
    height: 100%;
    flex-flow: column;
    position: relative;
}
#r4w_editor_right .css-f5dsf0sdsdf {
     right: 20px;
    cursor: pointer;
    margin: auto;
    margin-right: 0;
}
#r4w_editor_right .css-f5dsf0sdsdf svg {
    height: 20px;
    width: auto;
    fill: #FA6742;
}
#r4w_box-keyword_secondary #keyword_select_list h3{
     font-size: 18px;
}
#r4w_editor_right .box_keywords .title_keyword {
    font-size: 13px;
    margin-bottom: 0px;
    color: #FA6742;
    font-weight: 500;
    margin: auto;
    margin-left: 0;
}
#r4w_editor_right .box_keywords .box_title{
     display:flex;
}
#r4w_box-keyword_secondary #keyword_select_list h3{
     margin:20px 0;
}
.css-8d936c0b7f1b{
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
}
.css-8d936c0b7f1b svg{
    height: 100px;
    width: auto;
    text-align: center;
    margin: auto;
}
.css-926b8c83e35a{
    border-radius: 5px;
    text-align: center;
}
.css-926b8c83e35a p{
    font-size: 15px;
    line-height: 20px;
}
.css-903064e073dd{
    display: flex;
}
.css-903064e073dd button{
    margin-right: 20px;
}
.css-903064e073dd button{
    margin-left: 20px;
}
.css-e1ac03b9fa47{
    color:#fff;
    display:flex;
}
.css-4c9fef1458fc{
    width: 50%;
    margin-left: 10px;
    margin-right: 10px;
    background: #FA6742;
    padding: 15px;
    border-radius: 10px;
}
.css-17bb6ba233f6{
    font-size: 30px;
    font-weight: bold;
    display: contents;
}
.css-42dc08e64811{
    font-size: 14px;
}
#r4w_box-checkstr #loading_check #loading{
    display:block;
}
#r4w_box-checkstr #str_inconclusive,#r4w_box-checkstr #str_conclude{
    display:none;
}
#r4w_box-checkstr #str_inconclusive ul {
    width:100%;
    height:20px;
    text-align:center;
    display: contents;
}
#r4w_box-checkstr #str_inconclusive li {
    display:inline-block;
    *display:inline; /*IE7*/
    *zoom:1; /*IE7*/
    background:#FA6742;
    color:white;
    margin-right:10px;
    padding:10px;
}
#r4w_box-checkstr #str_conclude svg, #r4w_box-checkstr #str_inconclusive svg{
    height: 80px;
    width: auto;
    margin: auto;
    text-align: center;
    fill:#FA6742;
}
#r4w_box-checkstr .css-77d6884f9a1e{
    text-align: center;
}
#r4w_box_editor .css-d2d9c05240dc{
    text-align: center;
    font-size: 20px;
    position: relative;
    padding: 15px 20px;
    color: #fa6742;
    font-weight: 500;
    text-transform: uppercase;
    top: 11px;
}
#r4w_editor_page_info .css-e823bb193193{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: -8px 7px 17px 0 rgb(86 84 84 / 64%);
    border-radius: 5px;
    position: absolute;
    z-index: 200;
    width: calc(100% - 52px);
    min-width: 370px;
    max-height: 80%;
    margin: auto;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    overflow-x: hidden;
    overflow-y: auto;
}
#wpfooter{
    display:none;
}