<div id="r4w_st_page_file_editor" class="page_tab">
	<div id="r4w_box_subtabs" class="box_subtab">
		<ul>
			<li id="subpage_htaccess" class="btn_subtab">.htaccess</li>
			<li id="subpage_robots" class="btn_subtab">Robots.txt</li>
		</ul>
	</div>
	<div id="r4w_box_subpages">
		<div id="r4w_sst_subpage_htaccess" class="page_subtab">
			<label class="css-gfejhyi850 title_gwp">
				{_('File')} : htaccess
			</label>
			<div class="r4w_bullet_info">
				<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
				<div class="css-df5r0grg">
					{_('The ".htaccess" file is a configuration file, which allows you to define rules on your wordpress such as the possibility to create an address structure for your permalinks and archives')}.
				</div>
			</div>
			<div class="css-warning_reset">
				<div class="css-d5f0ef50ef">[@svg_warning]</div>
				<div class="css-df5pzek20">{_('Be careful when editing this file. These changes may prevent your wordpress, your themes or plugin from working properly. Make these changes only if you are safe without causing problems')}.</div>
			</div>
			[@rs_file_editor_htaccess_txt]
			[@tools_file_editor_htaccess_input]
			[@tools_file_editor_htaccess_btn]
		</div>
		<div id="r4w_sst_subpage_robots" class="page_subtab">
			<label class="css-gfejhyi850 title_gwp">
				{_('File')} : robots
			</label>
			<div class="r4w_bullet_info">
				<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
				<div class="css-df5r0grg">
					{_('The "robots.txt" file tells search engine crawlers the pages or files they can or can not browse from the wordpress')}.
				</div>
			</div>
			[@rs_file_editor_robots_txt]
			[@tools_file_editor_robots_input]
			[@tools_file_editor_robots_btn]
		</div>
	</div>	
</div>