<div id="r4w_st_page_sa_special_page" class="page_tab">
	<div id="r4w_box_subtabs" class="box_subtab">
		<ul>
			<li id="subpage_search" class="btn_subtab">{_('Search')}</li>
			<li id="subpage_error404" class="btn_subtab">{_('Error 404')}</li>
		</ul>
	</div>
	<div id="r4w_box_subpages">
		<div id="r4w_sst_subpage_search" class="page_subtab">
			<label class="css-gfejhyi850 title_gwp">
				{_('Page : Search')}
			</label>
			<div class="r4w_bullet_info">
				<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
				<div class="css-df5r0grg">
					{_('You can customize your meta title and default description for the search')}.
				</div>
			</div>	
			<div class="r4w_atwho_box select-field">
				<label for="bac_spp_pse_title">{_('Meta Title')}</label>
				<div class="btn_add_tag"><div class="css-25c58628966c">[@svg_add_more]</div></div>
				<div class="r4w_autosave_info r4w_progress_div">
					<input class="athow_input r4w_autosave" name="seo_settings|special_page|search|meta_title"></input>
					<div contenteditable="true" data-tag="[@tag_data_search]"  class="athow_content">[@search_meta_title]</div>
				</div>
			</div>
		</div>
		<div id="r4w_sst_subpage_error404" class="page_subtab">
			<label class="css-gfejhyi850 title_gwp">
				{_('Page : Error 404')}
			</label>
			<div class="r4w_bullet_info">
				<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
				<div class="css-df5r0grg">
					{_('You can customize your meta title and default description for the error 404')}.
				</div>
			</div>		
			<div class="r4w_atwho_box select-field">
				<label for="bac_spp_per_title">{_('Meta Title')}</label>
				<div class="btn_add_tag"><div class="css-25c58628966c">[@svg_add_more]</div></div>
				<div class="r4w_autosave_info r4w_progress_div">
					<input class="athow_input r4w_autosave" name="seo_settings|special_page|error_404|meta_title"></input>
					<div contenteditable="true" data-tag="[@tag_data_error404]" class="athow_content">[@error_404_meta_title]</div>
				</div>
			</div>
		</div>
	</div>
</div>