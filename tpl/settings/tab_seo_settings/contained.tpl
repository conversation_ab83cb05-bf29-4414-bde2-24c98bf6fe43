<div id="r4w_tab_seo_settings" class="tab_content" style="display:none;">
	<div class="tab-block">
		<div class="css-sdf5e0fg">
			<div class="css-ghyroi058">[@svg_tab_seo_settings]</div>
			<div>{_('Seo settings')}</div>
		</div>
		<div class="css-dsf5df0g">
			<div class="css-jtkgf80ertg">
				<div id="r4w_box_tabs" class="css-hjytert0e">
					<ul class="css-fdgfeg0hkuo">
						<li id="page_sa_basic_config" class="btn_tab">
							<div class="ico_tab css-fdgfd0f8rh">[@svg_tss_page_basic_config]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('Basic configuration')}</div>
								<div class="r4w_btn_sutitle">{_('General settings')}</div>
							</div>
						</li>
						<li id="page_sa_type" class="btn_tab" data-subtab="page_sa_type">
							<div class="ico_tab css-sd5ef0fv">[@svg_tss_page_type]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('Types of content')}</div>
								<div class="r4w_btn_sutitle">{_('Settings')}</div>
							</div>
						</li>
						<li id="page_sa_taxonomy" class="btn_tab" data-subtab="page_sa_taxonomy">
							<div class="ico_tab css-ryt50ert">[@svg_tss_page_taxonomy]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('Taxonomies')}</div>
								<div class="r4w_btn_sutitle">{_('Settings')}</div>
							</div>
						</li>
						<li id="page_sa_archive" class="btn_tab" data-subtab="page_sa_archive">
							<div class="ico_tab css-erhu0ze5">[@svg_tss_page_archive]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('Archive')}</div>
								<div class="r4w_btn_sutitle">{_('Settings')}</div>
							</div>
						</li>
						<li id="page_sa_special_page" class="btn_tab" data-subtab="page_sa_special_page">
							<div class="ico_tab css-ghrhr0rtr">[@svg_tss_page_special_page]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('Special page')}</div>
								<div class="r4w_btn_sutitle">{_('Settings')}</div>
							</div>
						</li>
						<li id="page_sa_rss" class="btn_tab">
							<div class="ico_tab css-g9dgrui0xc">[@svg_tss_page_rss]</div>
							<div class="css-sd5g0rg80g">
								<div class="r4w_btn_title">{_('RSS')}</div>
								<div class="r4w_btn_sutitle">{_('Settings')}</div>
							</div>
						</li>
					</ul>
				</div>
				<div id="r4w_box_pages" class="css-d5s0ds5gu">
					[@tab_basic_config]
					[@tab_type]
					[@tab_taxonomy]
					[@tab_archive]
					[@tab_special_page]
					[@tab_rss]
				</div>
			</div>
		</div>
	</div>
</div>
