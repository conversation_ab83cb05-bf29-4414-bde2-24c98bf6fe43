<div id="r4w_st_page_sa_rss" class="page_tab">
	<label class="css-gfejhyi850 title_gwp">
		{_('RSS feed settings')}
	</label>
	<div class="r4w_bullet_info">
		<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
		<div class="css-df5r0grg">
			{_('This feature is used to automatically add content to your RSS feeds, helping search engines identify your site as the origin of the content')}.
		</div>
	</div>
	<div class="r4w_atwho_box select-field athow_lg">
		<label for="bac_rss_before">{_('Content to add before the article')}</label>
		<div class="btn_add_tag"><div class="css-25c58628966c">[@svg_add_more]</div></div>
		<div class="r4w_autosave_info r4w_progress_div">
			<input class="athow_input r4w_autosave" name="seo_settings|rss_feed|content_before"></input>
			<div contenteditable="true" data-tag="[@tag_data]" class="athow_content">[@rss_feed_content_before]</div>
		</div>
	</div>
	<div class="r4w_atwho_box select-field athow_lg">
		<label for="bac_rss_after">{_('Content to add after the article')}</label>
		<div class="btn_add_tag"><div class="css-25c58628966c">[@svg_add_more]</div></div>
		<div class="r4w_autosave_info r4w_progress_div">
			<input class="athow_input r4w_autosave" name="seo_settings|rss_feed|content_after"></input>
			<div contenteditable="true" data-tag="[@tag_data]" class="athow_content">[@rss_feed_content_after]</div>
		</div>
	</div>
</div>