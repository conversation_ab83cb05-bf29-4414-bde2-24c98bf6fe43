<div id="r4w_st_page_sn_facebook" class="page_tab">
	<label class="css-gfejhyi850 title_gwp">
		Facebook
	</label>
	<div class="r4w_bullet_info">
		<div class="css-sd5r0fze5">[@svg_bullet_info]</div>
		<div class="css-df5r0grg">{_('Allows you to set up the social network to provide a better user experience and optimize your sharings')}.</div>
	</div>
	<div class="switch r4w_autosave_info">
		<label>
			<input type="checkbox" class="r4w_autosave" name="social_networks|facebook|editing_meta" [@facebook_editing_meta]>
			<span class="lever"></span>
			{_('Allow editing the meta tag in Article / Page')}
		</label>
	</div>
	<div class="r4w_additional_info">
		{_('This option allows you to manually edit the meta title and description for facebook when writing')}.
	</div>
	<div class="input-field s12">
		<div class="r4w_atwho_box select-field">
			<label for="url_facebook">{_('URL of the Facebook page')}</label>
			<div class="r4w_autosave_info r4w_progress_div dsgn_input_athow">
				<input class="athow_input r4w_autosave" name="social_networks|facebook|url_page"></input>
				<div contenteditable="true" class="athow_content">[@facebook_url_page]</div>
			</div>
		</div>
	</div>
	<div class="input-field s12">
		<div class="r4w_atwho_box select-field">
			<label for="app_id_facebook">{_('Facebook App ID')}</label>
			<div class="r4w_autosave_info r4w_progress_div dsgn_input_athow">
				<input class="athow_input r4w_autosave" name="social_networks|facebook|app_id"></input>
				<div contenteditable="true" class="athow_content">[@facebook_app_id]</div>
			</div>
		</div>
	</div>
</div>