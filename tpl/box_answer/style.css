#r4w_box-analyzes_answer.SimpleModalJs{
    max-width:700px;
}
#r4w_box-analyzes_answer .css-sfo82ac{
    font-size: 25px;
    text-align: center;
    color: #FA6742;
    padding: 10px;
}
#r4w_box-analyzes_answer .r4w_bullet_info{
    padding: 10px;
    background: #f3f3f3;
    margin-top: 20px;
}
#r4w_box-analyzes_answer .ph-item{
    border: 0;
    padding: 0;
    margin-bottom:0;
    text-align: center;
    margin: auto;
}
#r4w_analyzes_answer{
    max-height: 300px;
    min-height: 300px;
    overflow-x: hidden;
}
#r4w_analyzes_answer .arrow_more {
    margin-right: 8px;
    -webkit-transition: -webkit-transform .3s ease;
    transition: -webkit-transform .3s ease;
    -o-transition: transform .3s ease;
    transition: transform .3s ease;
    transition: transform .3s ease,-webkit-transform .3s ease;
}
#r4w_analyzes_answer .rotate-z {
    -webkit-transform: rotateZ(90deg);
    -ms-transform: rotate(90deg);
    transform: rotateZ(90deg);
}
#r4w_analyzes_answer img.arrow_more {
    height: 9px;
    margin-top: 5px;
}
#r4w_analyzes_answer .list_document .js-diary-show-details {
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    border-bottom: 1px solid #ededed;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}
#r4w_analyzes_answer .list_document {
    border-top: 0;
    background: #edeff2;
    margin-bottom: 10px;
}
#r4w_analyzes_answer .col_7mWSf {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: wrap;
    flex-flow: wrap;
    -ms-flex-align: center;
    align-items: center;
}
#r4w_analyzes_answer .list_document_answers{
    display:none;
}
#r4w_analyzes_answer .list_document_answers div:nth-child(2n) {
    background-color: #fbfcfe;
}
#r4w_analyzes_answer .list_document_answers div:nth-child(2n+1) {
    background-color: #f5f6f7;
}
#r4w_analyzes_answer .list_document_answers div {
    padding: 10px;
    font-weight: 400;
}
#r4w_analyzes_answer .idea_content{
    margin-right:15px;
}
.ph-item .ph-box-answer{
    display: inherit;
    float: inherit;
    padding: 0;
    flex-flow: inherit;
}
.ph-item .ph_answer{
    height: 39px;
    width: 100%;
    margin-bottom: 10px;
    background-color: #ced4da;
}

#r4w_analyzes_answer .css-5fe05f5z0f{
    font-size: 20px;
}
#r4w_analyzes_answer .css-ferg5r0hg{
    font-size: 13px;
    margin-top: 10px;
}
#r4w_analyzes_answer .css-5f0efeg5eg{
    text-align: center;
    padding: 10px;
}
#r4w_analyzes_answer .css-5f0efeg5eg svg{
    width: 40px;
    fill: #434343;
}
#r4w_analyzes_answer .css-sdf5e0f5ezfz{
    margin-top: 15px;
}
#r4w_analyzes_answer a.css-sd520sqd5z {
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_analyzes_answer a.css-sd520sqd5z:hover {
    background: #444444;
}
#r4w_analyzes_answer .blurring{
    color: transparent;
    text-shadow: 0 0 11px rgba(0,0,0,0.5);
    transition: all 0.5s;
    -webkit-user-select: none; /* Chrome all / Safari all */
    -moz-user-select: none;    /* Firefox all             */
    -ms-user-select: none;     /* IE 10+                  */
     user-select: none;        /* Likely future           */
}
#r4w_analyzes_answer .css-5fe05f5z0f{
    font-size: 20px;
}
#r4w_analyzes_answer .css-ferg5r0hg{
    font-size: 13px;
    margin-top: 10px;
}
#r4w_analyzes_answer .css-d50f65efe{
    background: #ffffffb0;
    margin: auto;
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translate(-50%, 0);
    max-width: 470px;
}
#r4w_analyzes_answer .css-5f0efeg5eg{
    text-align: center;
    padding: 15px;
}
#r4w_analyzes_answer .css-5f0efeg5eg svg{
    width: 40px;
    fill: #434343;
}
#r4w_analyzes_answer .css-sdf5e0f5ezfz{
    margin-top: 15px;
}
#r4w_analyzes_answer a.css-sd520sqd5z {
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_analyzes_answer a.css-sd520sqd5z:hover {
    background: #444444;
}
#r4w_analyzes_answer .css-f5e0f5ezfze{
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    border-bottom: 1px solid #ededed;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}
#r4w_analyzes_answer .css-f5e0f85ef {
    padding: 20px;
    font-size: 15px;
    text-align: center;
    color: #FA6742;
    font-weight: 500;
}