<div>
	<div class="css-df5e0f5zef">
		<div class="css-z5fze0f5ez"></div>
		<div class="css-s4df0ez4fe">{_('System requirements')}</div>
	</div>
	<div class="css-s5d0efeezg">
		<div class="css-f5e0rg5ree">
			<div>{_('Here is the basic system requirements to install rank4win on your wordpress')}.</div>
			<div>{_('If your hosting or WordPress does not meet this configuration, <b>you may have problems on your website</b> plus you were not getting the most out of rank4win, to continue <b> you need to upgrade your configuration</b>')} !</div>
		</div>
		[@result_config]
		<div class="css-5d0df5e">
			<div class="css-5d0e5feeb">{_('Hosting and performance')} :</div>
			<ul>
				<li>
					<div class="css-d5e0df5e">
						<div class="css-5zs0d5fer">{_('Php version required')} : </div>
						<div class="css-io50geezf [@version_php_color]">[@version_php_result]</div>
					</div>
				</li>
				<li>
					<div class="css-d5e0df5e">
						<div class="css-5zs0d5fer">{_('Wordpress version required')} : </div>
						<div class="css-io50geezf [@version_wp_color]">[@version_wp_result]</div>
					</div>
				</li>
				<li>
					<div class="css-d5e0df5e">
						<div class="css-5zs0d5fer">{_('Rank4win version required')} : </div>
						<div class="css-io50geezf [@version_r4w_color]">[@version_r4w_result]</div>
					</div>
				</li>
				<li>
					<div class="css-d5e0df5e">
						<div class="css-5zs0d5fer">{_('Available domain name')} : </div>
						<div class="css-io50geezf [@domain_color]">[@domain_result]</div>
					</div>
				</li>
				<li>
					<div class="css-d5e0df5e">
						<div class="css-5zs0d5fer">{_('HTTPS protocol')} : </div>
						<div class="css-io50geezf [@https_color]">[@https_result]</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="css-5d0df5e">
			<div class="css-5d0e5feeb">{_('Plugin')} :</div>
			<ul>
				<li>
					{_('No conflicts with an installed plugin')}
				</li>
			</ul>
		</div>
	</div>
</div>
