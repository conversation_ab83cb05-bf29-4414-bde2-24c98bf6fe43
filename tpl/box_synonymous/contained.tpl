<div class="SimpleModalJs" id="r4w_box-analyzes_synonymous" data-document="[@document_uuid]">
	<div class="smj-header">
	    <div class="logo">
	      [@svg_rank4win_white]
	    </div>
	</div>
	<div class="smj-content">
		<div class="css-sfo82ac">
			{_('Synonymous')}
		</div>
		<div class="r4w_bullet_info">
			<div class="css-sd5r0fze5">
				[@svg_bullet_info]
			</div>
			<div class="css-df5r0grg">
				{_('The synonyms can find terms more adapted to the context than those used spontaneously. It also avoids a repetition of words in the same text to improve the style of its writing')}.
			</div>
		</div>
		<div class="kic_box_search">
            <div class="kic_input_search">
                <input id="r4w_synonymous_word" name="synonymous_word" type="text" placeholder="{_('Enter a word')}">
            </div>
            <a href="#" id="btn_synonymous" class="kic_search_btn">
                <svg fill="currentColor" preserveAspectRatio="xMidYMid meet" height="1em" width="1em" viewBox="0 0 40 40" style="vertical-align: middle;"><g><path d="m27.2 18.6q0-4.2-2.9-7.1t-7.1-2.9-7 2.9-3 7.1 2.9 7 7.1 3 7.1-3 2.9-7z m11.4 18.5q0 1.2-0.8 2.1t-2 0.8q-1.2 0-2-0.8l-7.7-7.7q-4 2.8-8.9 2.8-3.2 0-6.1-1.3t-5-3.3-3.4-5-1.2-6.1 1.2-6.1 3.4-5.1 5-3.3 6.1-1.2 6.1 1.2 5 3.3 3.4 5.1 1.2 6.1q0 4.9-2.7 8.9l7.6 7.6q0.8 0.9 0.8 2z"></path></g></svg>
            </a>
        </div>
		<div id="r4w_analyzes_synonymous">
			<div class="analyzes_synonymous"></div>
		</div>
	</div>
</div>