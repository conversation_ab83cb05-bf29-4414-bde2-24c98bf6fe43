<div id="r4w_system_message"></div>
<div id="r4w_auth_login">
  <div class="css-100frqb">
      <div class="css-9388ij">
          <div class="css-1lekzkb">
            <div class="css-1rvpz4w">
            	[@svg_rank4win_black]
            </div>
            <a class="css-5f0df50ez" href="[@url_create_account]">{_('Sign up here')}.</a>
          </div>
          <div class="css-12kz5vy">
              <div id="r4w_error"></div>
              <div name="email" id="email" class="css-4ywf4y">
                  <label for="user" class="css-ya34dr euzoycu0">{_('Email Address')}</label>
                  <div style="position: relative;">
                      <input id="user" name="user" type="email" placeholder="[@placeholder_email]" class="css-1rkhnpn r4wfix_wp_input" autocomplete="new-password" tabindex="1">
                  </div>
              </div>
              <div name="password" type="password" id="password" class="css-17maygl">
                  <label for="pwd" class="css-ya34dr euzoycu0">
                    <span>
                      {_('Password')}
                      <a class="css-z73n5a e1gn6usl0" href="[@url_forgot]"> ({_('Forgot')} ?)</a>
                    </span>
                  </label>
                  <div style="position: relative;">
                      <input id="pwd" name="pwd" type="password" placeholder="[@placeholder_password]" class="css-1rkhnpn r4wfix_wp_input" autocomplete="new-password" tabindex="2">
                  </div>
              </div>
              <div id="loading"><div class="dual-ring"></div></div>
              <button id="btn_submit" class="css-1g97g96" tabindex="3">{_('Login Now')}</button>
          </div>
          <div class="css-1yuhvjn">
            <a class="css-1rvpz4w" target="_blanc" href="[@r4w_url_privacy]">
              {_('Terms of Service')} &amp;
              {_('Privacy Policy')}</a>
          </div>
      </div>
  </div>
</div>