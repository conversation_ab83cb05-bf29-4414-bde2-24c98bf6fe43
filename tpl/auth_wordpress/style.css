.rank4win_logo_svg-1{
	fill:#404040;
}
.rank4win_logo_svg-2{
	fill:#FA6742;
}
.css-1lekzkb {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
}
.css-5f0df50ez{
    background: #F11460;
    color: #fff;
    padding: 10px;
    text-decoration: none;
    opacity: 1;
    font-weight: 600;
    font-size: 17px;
    border-radius: 10px;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
}
.css-1rvpz4w {
    color: #636363;
    opacity: 0.8;
    transition: opacity 0.2s ease-in-out 0s;
}
.css-1rvpz4w:hover {
    color: #636363;
    text-decoration: none;
    opacity: 1;
}
.css-100frqb {
    height: 100%;
    width: 100%;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    padding: 20px 0px;
    background-repeat: no-repeat;
    background-position: center 100%;
}
.css-1ux7ccw {
    display: inline-flex;
    -webkit-font-smoothing: antialiased;
    vertical-align: middle;
    text-align: center;
    -webkit-appearance: none;
    cursor: pointer;
    font-family: inherit;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-weight: bold;
    background-color: #565656;
    color:#fff;
    font-size: 14px;
    margin-top: 32px;
    outline: none;
    text-decoration: none;
    transition: all 0.2s ease-in-out 0s;
    border-width: 0px;
    border-radius: 4px;
    border-color: #0c2f55;
    padding: 16px;
    width: 100%;
}
.css-1ux7ccw:hover{
    background:#FA6742
}
.css-12kz5vy {
    margin-top: 1rem;
    margin-bottom: 1rem;
    width: auto;
    background-color: rgb(255, 255, 255);
    box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 8px 0px, rgba(0, 0, 0, 0.08) 0px 2px 4px 0px;
    padding: 32px;
    border-radius: 6px;
}
.css-1rkhnpn {
    color: rgb(14, 10, 46);
    font-size: 14px;
    box-shadow: none;
    line-height: 1rem;
    width: 100%;
    padding: 0.75rem;
    outline: none;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(225, 229, 237);
    border-radius: 4px;
    transition: all 0.2s ease-in-out 0s;
}
.css-ya34dr {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #636363;
    margin: 0.5rem 0px;
    text-align: left;
}
.css-n9jk6 {
    text-align: left;
    color: rgb(255, 238, 233);
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 4px;
    background: rgb(255, 86, 39);
}
.auth_login_controller .css-1rkhnpn {
    color: rgb(14, 10, 46);
    font-size: 14px;
    box-shadow: none;
    line-height: 1rem;
    width: 100%;
    padding: 0.75rem;
    outline: none;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(225, 229, 237);
    border-radius: 4px;
    transition: all 0.2s ease-in-out 0s;
}
@media screen and (min-width: 40em){
	.css-9388ij {
	    width: 450px;
	}
}
.css-sd5z8zed2,.css-sdf25ed6s58,.css-trt8e2e7{
    margin:auto;
}
.css-s585e2e8cefg{
    display:flex;
    width:100%;
}
.css-sd5z8zed2 svg{
    height: 50px;
    width:auto;
    fill: #0087be;
}
.css-sdf25ed6s58 svg{
    height:50px;
    width:auto;
}
.css-trt8e2e7 svg{
    height:30px;
    width:auto;
}
.css-d5fe8fiy{
    margin:auto;
}
.css-cdde8yy{
    font-size: 12px;
    color: #969696;
}
.css-f5s0fdsz{
    background: #565656;
    color: #fff;
    padding: 10px;
    border-radius: 10px;
    font-weight: 500;
}
.css-erger5e0te{
    background: #565656;
    color: #fff;
    padding: 10px;
    border-radius: 10px;
    font-weight: 500;
}
.css-df52e0e{
    height:50px;
    line-height: 65px;
    height: 65px
}
.admin_page_r4w_auth_wordpress .r4w_msg_opr_maintenance {
    position: relative;
    display: flex;
    margin: 10px 20px 20px 2px;
}