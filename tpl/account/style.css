#r4w_tab_account .ph-item{
    border:0;
    margin: 0;
    padding: 0;
    width:100%;
}
#r4w_tab_account .ph-line{
    flex: inherit;
    padding: 0;
    width: 100%;
}
#r4w_tab_account .ph_next_billing{
    height: 45px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-bottom:0;
}
#r4w_tab_account .ph_title{
    height: 20px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-bottom: 5px;
}
#r4w_tab_account .ph_sub_date{
    height: 75px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-bottom:0;
}
#r4w_tab_account .ph_sub_btn{
    height: 40px;
    background-color: #ced4da;
    width:230px;
    margin: auto;
}
#r4w_tab_account .ph_email{
    height: 20px;
    background-color: #ced4da;
    margin-bottom: 5px;
}
#r4w_tab_account .ph_firstname{
    height: 20px;
    background-color: #ced4da;
    margin-bottom: 5px;
}
#r4w_tab_account .ph_lastname{
    height: 20px;
    background-color: #ced4da;
}
#r4w_tab_account.tab_content{

}
#r4w_tab_account .css-5d0f5eg0er{
     display: flex;
     width: 100%;
}
#r4w_tab_account .box_cnt_solo {
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    margin:auto;
    margin-bottom: 20px;
    margin-top: 20px;
    height:max-content;
    min-height: 35px;
}
#r4w_tab_account .css-sfq5s0gfedgs{
    width:330px;
}
#r4w_tab_account .css-sf5egf0ef{
    width:80px;
    opacity: 0.9;
}
#r4w_tab_account .css-sf5e0fvr{
    padding: 5px;
    background: #FA6742;
    color: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    min-width: 70px;
    text-align: center;
}
#r4w_tab_account .css-sfd5gf0e{
    background: #f1f1f1;
    padding: 5px;
    font-size: 25px;
    font-weight: 800;
    min-width: 70px;
    text-align: center;
    padding-top: 15px;
    padding-bottom: 15px;
    color: #444;
    border-top-left-radius: 10px;
}
#r4w_tab_account .css-fe0e5fge{
    background: #f1f1f1;
    padding: 5px;
    border-bottom-right-radius: 10px;
    min-width: 70px;
    text-align: center;
    border-top: 1px dashed #b3b3b3;
    text-transform: uppercase;
    color: #444;
}
#r4w_tab_account .css-sf52d0fefe{
    margin-left: 15px;
}
#r4w_tab_account .css-f5e0fgegve{
    font-size: 22px;
    font-weight: 600;
    width: 100%;
    color: #444;
    margin: auto;
}
#r4w_tab_account .css-sq65df6fdv{
    padding-top: 10px;
    font-size: 13px;
    color: #444;
}
#r4w_tab_account .css-sf50fref6e0r{
    padding-top: 20px;
}
#r4w_tab_account .svg{
    height: 65px;
    width: auto;
    text-align: center;
    fill: #444;
}
#r4w_tab_account a.css-sd520sqd5z{
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_tab_account a.css-df5e0fbv5rr{
     border: none;
    outline: none;
    color: #fff;
    background: #F44336;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_tab_account a.css-sd520sqd5z:hover {
    background: #444444;
}
#r4w_tab_account .css-5fedf0e85f{
    display:flex;
}
#r4w_tab_account .css-5fe0f5eze{
    margin-top: 20px;
    margin-bottom: 10px;
}
#r4w_tab_account .css-sfd5gf0e svg,#r4w_tab_account .css-sf5e0fvr svg,#r4w_tab_account .css-fe0e5fge svg{
    height: 14px;
    width: auto;
    fill: #444;
}
#r4w_tab_account .css-sf52d0fefe{
    height:100%;
    width:100%;
}
#r4w_tab_account .billing_issue{
     background: #F44336;
     padding: 7px;
     text-align: center;
     display: flex;
     margin-top: 0px;
     margin-bottom: 10px;
     border-radius: 5px;
}
#r4w_tab_account .css-fd5e0fe5fe{
     text-align: center;
     width: 100%;
     color: #929292;
     font-size: 14px;
     font-weight: 500;
     text-decoration: none;
     margin: auto;
     display: flow-root;
     margin-top: 15px;
}
#r4w_tab_account .billing_gift{
    background: #4cbd87;
    padding: 5px;
    text-align: center;
    display: flex;
    margin-top: 0px;
    margin-bottom: 10px
}
#r4w_tab_account .css-sf56ef0ef{
    margin: auto;
    margin-right: 15px;
    margin-left: 0;
}
#r4w_tab_account .css-sf56ef0ef svg{
    height: 35px;
    width: auto;
    fill: #fff;
}
#r4w_tab_account .css-f5e0zf5zef{
    margin: auto;
    text-align: left;
    color: #ffffff;
    margin-left: 0;
    margin-right: 10px;
    width:100%;
}
#r4w_box_account ul#r4w-tabs{
    display: flow-root;
}
#r4w_box_account ul#r4w-tabs li{
    float: left;
}
#r4w_box_account ul#r4w-tabs a.another_account{
    user-select: none;
    cursor: pointer;
    list-style: none;
    background: #F44336;
    color: #ffffff;
    margin-bottom: 5px;
    padding: .5rem 1rem;
    margin: 0 3px 0 0;
    font-weight: 600;
    text-decoration: none;
    float: right;
}
#r4w_box_account .box_cnt {
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    display: flex;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
}
#r4w_tab_invoice .box_cnt{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    display: inherit;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
}
#r4w_tab_wordpress .box_cnt{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    display: flex;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
    display: inherit;
}
#r4w_box_account .css-df5dsf0effet {
    font-size: 22px;
    font-weight: 600;
    width: 100%;
    color: #444;
    margin: auto;
}
#box_subscription .credit_applied{
    text-decoration: line-through;
}
#r4w_tab_wordpress .css-df5d05dfe{
    background: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    display: flex;
    margin-top: 10px;
}
#r4w_tab_wordpress .css-df5d05dfe:first-child{
    margin-top:0;
}
#r4w_tab_wordpress .cnt_list{
    margin:auto;
}
#r4w_tab_wordpress .css-sdqsdqgtet{
    margin-left: 0;
    width: 100%;
}
#r4w_tab_wordpress .css-f5e0df5ef{
    font-size: 20px;
    font-weight:600;
    margin-bottom: 5px;
}
#r4w_tab_wordpress .css-f5g0e8g0zrv{
    margin-right:0;
}
#r4w_tab_wordpress .css-d50dfgrezt{
    display: flex;
}
#r4w_tab_wordpress .css-d50dfgrezt svg{
    height:30px;
    width:auto;
    fill:#F44336;
}
#r4w_tab_wordpress .box_cnt{
    padding: 10px 10px;
    background: #ffffff;
    box-shadow: 0 3px 7px 0 rgba(148, 148, 148, 0.3);
    border-radius: 5px;
    margin-bottom: 20px;
    margin-top: 20px;
    height: auto;
    min-height: 35px;
    display: inherit;
}
#r4w_tab_wordpress .css-sfd50ff6efd{
    margin-right: 10px;
}
#r4w_tab_wordpress .css-f5e0df5ef{
    margin: auto;
}
#r4w_tab_wordpress .css-f5e0df5ef{
    font-size: 20px;
    font-weight:600;
    margin-bottom: 5px;
}
#r4w_tab_wordpress .css-f5d0fe5g0g{
    padding: 10px;

    background: #f9f9f9;
    margin-top: -12px;
    border-top: 1px solid #e2e2e2;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
}
#r4w_tab_wordpress .css-s5f0eg65rez{
    display:flex;
    margin-left:5px;
}
#r4w_tab_wordpress .css-f50ef5zefz{
    display: flex;
    margin-right: 10px;
}
#r4w_tab_wordpress .css-f50eg65rer{
    width: 20px;
    height: 10px;
    background: #9E9E9E;
    border-radius: 10px;
    margin: auto;
    margin-right: 5px;
}
#r4w_tab_wordpress .r4w_wpsync_green{
    background:#8BC34A
}
#r4w_tab_wordpress .r4w_wpsync_orange{
    background:#FF9800
}
#r4w_tab_wordpress .r4w_wpsync_red{
    background: #F44336;
}
#r4w_tab_wordpress .r4w_wpsync_grey{
    background: #9E9E9E;
}
#r4w_tab_wordpress .r4w_btn_menu{
    margin:auto;
}
#r4w_tab_wordpress .ph_list_wordpress{
    height: 61px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-top: 10px;
}
#r4w_tab_wordpress .ph_list_wordpress:first-child{
    margin-top:0;
}
#r4w_tab_wordpress .ph-item>*{
    padding:0;
}
#r4w_tab_invoice .ph_list_invoice{
    height: 61px;
    background-color: #ced4da;
    width: 100%;
    margin: auto;
    margin-top: 10px;
}
#r4w_tab_invoice .ph_list_invoice:first-child{
    margin-top:0;
}
#r4w_tab_invoice .ph-item>*{
    padding:0;
}
#r4w_tab_wordpress .css-s5d0f0e5fe{
    font-size: 14px;
    text-align: center;
    margin: auto;
    padding: 5px;
    background: #9E9E9E;
    color: #fff;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    margin-top: -5px;
}
#r4w_tab_wordpress .r4w_show_menu{
    right:30px;
}
#r4w_tab_invoice .r4w_show_menu {
    right: 23px;
}
#r4w_tab_wordpress .r4w_wpbar_orange{
    background: #ff9800;
}
#r4w_tab_wordpress .r4w_star{
    margin: auto;
    margin-right: 10px;
}
#r4w_tab_wordpress .r4w_star svg{
    height: 30px;
    width: auto;
    fill:#ED8A19;
}
#r4w_tab_wordpress .css-5f0df5dsfve{
    background: #f1f1f1;
    padding: 10px;
    margin-bottom: -15px;
    width: max-content;
    margin-top: 10px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    color: #9e9e9e;
}
#r4w_box_add_subscription .css-sdfds2fs0,#r4w_box_change_subscription .css-sdfds2fs0,#r4w_box_new_subscription .css-sdfds2fs0 {
    font-size: 25px;
    text-align: center;
    color: #FA6742;
    padding: 10px;
}
#r4w_tab_wordpress .css-5fd0fgevvre{
    text-align: right;
    font-size: 13px;
    color: #FA6742;
}
#r4w_tab_wordpress .css-5fd0fgevvre b{
    font-size:20px;
}
#r4w_box_subscription .css-s5dsq0d5qsd{
    display: flex;
    margin-top: 20px;
    flex-flow: wrap;
}
#r4w_box_subscription a.css-sd520sqd5z{
    border: none;
    outline: none;
    color: #fff;
    background: #FA6742;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_box_subscription a.css-sd520sqd5z:hover {
    background: #444444;
}
#box_account .css-f5e0f5zefz{
    padding-top: 10px;
    padding-left: 15px;
    padding-right: 15px;
    line-height: 20px;
}
#box_account .css-f5e0f5zefz b{
    font-size: 15px;
}
#acc_detail svg{
    height: 11px;
    width: auto;
    fill: #444;
}
#r4w_box_manage_account .css-5fde0f5ezfd{
    display:none;
}
#r4w_invoices{
    width:100%;
}
#r4w_tab_invoice .cnt_list {
    margin: auto;
}
#r4w_tab_invoice .css-fe80ezuy {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    display: flex;
    margin-top: 10px;
}
#r4w_tab_invoice .css-sdqsdqgtet{
    display:flex;
}
#r4w_tab_invoice .css-5dze0d5efe, #r4w_tab_invoice .css-ze54f0ze5f0,#r4w_tab_invoice .css-f5e60fz5fz {
    font-size: 20px;
    font-weight: 600;
    margin: auto;
    margin-bottom: 5px;
}
#r4w_tab_invoice .css-5dze0d5efe {
    margin-left: 0;
    width:40%;
}
#r4w_tab_invoice .css-ze54f0ze5f0 {
    width:40%;
}
#r4w_tab_invoice .css-f5e60fz5fz {
    margin-right: 20px;
    width:20%;
}
#r4w_tab_invoice .css-sdqsdqgtet {
    margin-left: 0;
    width: 100%;
}
#r4w_invoices{
    height: calc(100vh - 440px);
}
#r4w_list_invoices{
    margin-right:15px;
}
#r4w_tab_invoice .css-zsfd50ef5e{
    display: flex;
    background: #FA6742;
    padding: 10px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #fff;
    margin-right: 15px;
}
#r4w_tab_invoice .css-fze5f0zyhi{
    width:40%;
}
#r4w_tab_invoice .css-o5lu0fdqsf{
    width:40%;
}
#r4w_tab_invoice .css-f4z0fezfzf{
    width:20%;
}
#r4w_tab_invoice .css-2ds0f5ezee{
    width: 35px;
}
#r4w_tab_invoice #loading .dual-ring{
    height: 30px;
    width: 30px;
}
#r4w_tab_invoice #loading{
    margin: 0;
}
#r4w_cancelled_sub .css-gh7h0revd{
     width: 100%;
     margin-top: 10px;
     margin-bottom: 10px;
}
#r4w_cancelled_sub .css-gh7h0revd textarea{
     width: 100%;
     background:#f3f3f3;
     padding:10px;
}
#r4w_cancelled_sub .css-5f0ze5gze{
    display: flex;
    margin-top:20px;
}
#r4w_cancelled_sub .css-f50ef5zefze{
     margin: auto;
     margin-left: 0;
     width: 50%;
     font-size: 18px;
     font-weight: 400;
     text-decoration: none;
     color: #f8755a;
}
#r4w_cancelled_sub .css-f5e0f5ezgyujo{
     width: 50%;
     margin: auto;
     margin-right: 0;
     -webkit-font-smoothing: antialiased;
     vertical-align: middle;
     text-align: center;
     -webkit-appearance: none;
     cursor: pointer;
     font-family: inherit;
     -webkit-box-align: center;
     align-items: center;
     -webkit-box-pack: center;
     justify-content: center;
     font-weight: bold;
     background-color: #bfbfbf;
     cursor: default;
     color: #fff;
     font-size: 14px;
     outline: none;
     text-decoration: none;
     transition: all 0.2s ease-in-out 0s;
     border-width: 0px;
     border-radius: 4px;
     border-color: #0c2f55;
     padding: 16px;
}
.css-g80rsh8reb{
     background: #565656;
     color: #fff;
     padding: 10px;
}
#r4w_cancelled_sub .css-f5e0f5ezgyujo.css-35f2ve0f{
     background-color: #565656;
     cursor: pointer;
}
#r4w_cancelled_sub .css-f5e0f5ezgyujo.css-35f2ve0f:hover{
     background-color:#FA6742;
}
#r4w_cancelled_sub #loading{
     margin:auto;
     margin-right:0;
}
#r4w_cancelled_sub #loading .dual-ring{
     width: 40px;
     height: 40px;
     margin-right: 35px;
}
#reactivate_sub #loading{
     margin:auto;
     margin-right:0;
}
#reactivate_sub #loading .dual-ring{
     width: 33px;
     height: 33px;
}
#r4w_tab_account a.css-f50ef5fe{
     border: none;
    outline: none;
    color: #636363;
    background: #ffffff;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor: pointer;
    width: max-content;
}
#r4w_tab_account #box_issue #loading{
     margin: 0;
     margin-right: 20px;
}
#r4w_tab_account #box_issue #loading .dual-ring {
    width: 30px;
    height: 30px;
    margin-left: -25px;
    margin-top: -25px;
    display: inline-block;
    content: " ";
    display: block;
    margin: 1px;
    border-radius: 50%;
    border: 5px solid #ffffff;
    border-color: #ffffff transparent #ffffff transparent;
    animation: dual-ring 1.2s linear infinite;
    margin: auto;
}
#r4w_tab_account #box_issue .css-5f0egf5geer{
     display:flex;
}
.css-f60e5zef{
     width: 41px;
     content: "";
     display: inline-block;
     height: 18px;
     background-repeat: no-repeat;
     vertical-align: top;
     margin-right:10px;
}
#r4w_tab_account .css-f50f5efe{
     display: flex;
     margin-top: 5px;
     border-bottom: 1px solid #eaeaea;
     border-top: 1px solid #eaeaea;
     padding: 5px;
}
#r4w_tab_account .css-f5e0zgfzegy{
     font-size: 15px;
     font-weight: 500;
     display: flex;
     width: 100%;
}
#r4w_tab_account .css-f05zef0edf{
     width: 50%;
}


#r4w_update_payment #payment-form{
    margin-top: 40px;
}
#r4w_update_payment .smj-content{
     background: #f7f8f9;
     padding: 15px;
}
#r4w_update_payment .css-sdfds2fs0{
     font-size: 25px;
     text-align: center;
     color: #FA6742;
     padding: 10px;
}
#r4w_update_payment .r4w_stripe_card{
    background: #f7f8f9;
    padding: 15px;
    border-radius: 8px 0 0 8px;
}
#r4w_update_payment .StripeElement {
  box-sizing: border-box;
  height: 40px;
  padding: 10px 12px;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}
#r4w_update_payment .StripeElement--focus {
  box-shadow: 0 1px 3px 0 #cfd7df;
}
#r4w_update_payment .StripeElement--invalid {
  border-color: #fa755a;
}
#r4w_update_payment  .StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}
#r4w_update_payment .form-row{
    margin-bottom:20px;
}
#r4w_update_payment .css-sd5sqd0qsd{
    height: initial;
    color: #9E9E9E;
    padding: 10px;
    position: relative;
    width: 100%;
}
#r4w_update_payment button {
    border: none;
    outline: none;
    color: #fff;
    background: #444444;
    white-space: nowrap;
    height: 40px;
    line-height: 40px;
    padding: 0 14px;
    box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
    border-radius: 4px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-decoration: none;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    margin: auto;
    text-align: center;
    display: inherit;
    cursor:pointer;
}
#r4w_update_payment button:hover {
    background: #FA6742;
}
#r4w_update_payment #card-errors {
    padding: 4px 0;
    color: #fa755a;
    font-size: 16px;
}

#r4w_update_payment .css-sdfs5f0sq{
    text-align: center;
    font-size: 11px;
    position: absolute;
    bottom: 0;
}
#r4w_update_payment .ph-item{
    background:initial;
    border:0;
    margin: 0;
}
#r4w_update_payment .ph-line{
    flex: inherit;
    padding: 0;
    width: 100%;
}
#r4w_update_payment .ph_card_title{
    height: 25px;
    background-color: #ced4da;
    width: 430px;
    margin: auto;
    margin-bottom:0;
    margin-top:0;
}
#r4w_update_payment .ph_card_number{
    height: 40px;
    background-color: #ced4da;
    width:auto;
    margin: auto;
    margin-bottom: 10px;
    margin-top: 40px;
    margin-left: 15px;
    margin-right: 15px;
}
#r4w_update_payment .ph_card_btn{
    height: 40px;
    background-color: #ced4da;
    width: 180px;
    margin: auto;
    margin-bottom: 0;
    margin-top: 20px;
}
#r4w_update_payment #loading_payment{
    display:none;
}
#r4w_update_payment .payment-loader .binding {
  content : '';
  width : 60px;
  height : 4px;
  border : 2px solid #9b72ee;
  margin : 0 auto;
}

#r4w_update_payment .payment-loader .pad {
  width : 60px;
  height : 38px;
  border-radius : 8px;
  border : 2px solid #FA6742;
  padding : 6px;
  margin : 0 auto;
}
#r4w_update_payment .payment-loader .loader-text{
    display: grid;
    margin-top: 10px;
}
#r4w_update_payment .loader-text p{
    font-size: 13px;
    text-align: center;
    line-height: 20px;
    color: #FA6742;
    margin: 0;
}
#r4w_update_payment .payment-loader .chip {
  width : 12px;
  height: 8px;
 background:#FA6742;
  border-radius: 3px;
  margin-top: 4px;
  margin-left: 3px;
}
#r4w_update_payment .payment-loader .line {
  width : 52px;
  margin-top : 6px;
  margin-left : 3px;
  height : 4px;
 background:#FA6742;
 border-radius: 100px;
  opacity : 0;
  -webkit-animation : stripe_writeline 3s infinite ease-in;
  -moz-animation : stripe_writeline 3s infinite ease-in;
  -o-animation : stripe_writeline 3s infinite ease-in;
  animation : stripe_writeline 3s infinite ease-in;
}
#r4w_update_payment .payment-loader .line2 {
  width : 32px;
  margin-top : 6px;
  margin-left : 3px;
  height : 4px;
     background : #FA6742;
  border-radius: 100px;
  opacity : 0;
  -webkit-animation : stripe_writeline2 3s infinite ease-in;
  -moz-animation : stripe_writeline2 3s infinite ease-in;
  -o-animation : stripe_writeline2 3s infinite ease-in;
  animation : stripe_writeline2 3s infinite ease-in;
}
#r4w_update_payment .payment-loader .line:first-child {
  margin-top : 0;
}
#r4w_update_payment .payment-loader .line.line1 {
  -webkit-animation-delay: 0s;
  -moz-animation-delay: 0s;
  -o-animation-delay: 0s;
  animation-delay: 0s;
}
#r4w_update_payment .payment-loader .line.line2 {
  -webkit-animation-delay: 0.5s;
  -moz-animation-delay: 0.5s;
  -o-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
@keyframes stripe_writeline {
  0% { width : 0px; opacity: 0; }
  33% { width : 52px; opacity : 1; }
  70% { opacity : 1; }
  100% {opacity : 0; }
}
@keyframes stripe_writeline2 {
  0% { width : 0px; opacity: 0; }
  33% { width : 32px; opacity : 1; }
  70% { opacity : 1; }
  100% {opacity : 0; }
}
#r4w_update_payment #error_payment{
     display: block;
     color: #F44336;
     text-align: center;
     margin-left: 15%;
     margin-right: 15%;
}
#r4w_update_payment #success_payment,#r4w_update_payment #error_payment{
    display:none;
}
#r4w_update_payment .thumbsup-icon {
    width: 180px;
    height: auto;
    position: relative;
    margin: auto;
}
#r4w_update_payment .thumbsup-icon svg {
    position: absolute;
}
#r4w_update_payment .thumbsup-icon .stars {
    fill: #febb02
}
#r4w_update_payment .thumbsup-icon .star1 {
    animation: 1300ms star-fly1;
    z-index: 1;
}
#r4w_update_payment .thumbsup-icon .star2 {
    animation: 1300ms star-fly2;
    z-index: 1;
}
#r4w_update_payment .thumbsup-icon .star3 {
    animation: 1300ms star-fly3;
    z-index: 1;
}
#r4w_update_payment .thumbsup-icon .star4 {
    animation: 1300ms star-fly4;
    z-index: 1;
}
#r4w_update_payment .thumbsup-icon .star5 {
    animation: 1300ms star-fly5;
    z-index: 1;
}
#r4w_update_payment .thumbsup-icon .thumbsup {
    animation: 1300ms thumbsup-shake 1 ease-in-out;
    z-index: 2;
}
@keyframes star-fly1 {
    0% {
        transform: translate(20%, 40%) scale(0.76, 0.76);
        opacity: 0;
    }
    12.8% {
        opacity: 1;
    }
    28.2% {
        transform: translate(0, 0) scale(1, 1);
    }
}
@keyframes star-fly2 {
    0% {
        transform: translate(-14%, 40%) scale(0.76, 0.76);
        opacity: 0;
    }
    12.8% {
        opacity: 1;
    }
    28.2% {
        transform: translate(0, 0) scale(1, 1);
    }
}
@keyframes star-fly3 {
    0% {
        transform: translate(5%, -35%) scale(0.76, 0.76);
        opacity: 0;
    }
    12.8% {
        opacity: 1;
    }
    28.2% {
        transform: translate(0, 0) scale(1, 1);
    }
}
@keyframes star-fly4 {
    0% {
        transform: translate(30%, 0) scale(0.76, 0.76);
        opacity: 0;
    }
    12.8% {
        opacity: 1;
    }
    28.2% {
        transform: translate(0, 0) scale(1, 1);
    }
}
@keyframes star-fly5 {
    0% {
        transform: translate(-20%, 0) scale(0.76, 0.76);
        opacity: 0;
    }
    12.8% {
        opacity: 1;
    }
    28.2% {
        transform: translate(0, 0) scale(1, 1);
    }
}
@keyframes thumbsup-shake {
    0% {
        transform: rotate(20deg) scale(0.74, 0.74);
        opacity: 0.1;
    }
    7.7% {
        opacity: 1;
        transform: rotate(0) scale(1, 1);
    }
    15.4% {
        transform: rotate(-11deg) scale(1.08, 1.08);
    }
    28.2% {
        transform: rotate(0) scale(1, 1);
    }
}
#r4w_list_invoices .css-5dze0d5efe svg {
    height: 25px;
    width: auto;
    fill: #444;
}
@media screen and (max-width: 782px){
     #r4w_tab_account .css-5d0f5eg0er{
          display: block;
     }
     #r4w_box_account ul#r4w-tabs{
          display:grid;
     }
     #r4w_box_account ul#r4w-tabs li{
          width: auto;
          margin-bottom: 5px;
          display: block;
     }
}