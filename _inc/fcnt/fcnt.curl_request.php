<?php 
	if ( ! function_exists( 'r4w_curl_request' ) ) {
		/**
		 * Permet de transférer et récupérer des données sur le réseau rank4win
		 */ 
		function r4w_curl_request($a){
			global $wpdb;
			$wp_table_app = $wpdb->prefix.r4w_bdd_table_app;

			// OFFLINE MODE: Return mock responses when API server is down
			if (defined('R4W_OFFLINE_MODE') && R4W_OFFLINE_MODE === true) {
				$mock_response = [
					"resp" => [
						"success" => [
							"data" => "offline_mode",
							"message" => "Operating in offline mode"
						]
					],
					"err" => "",
					"info" => ["http_code" => 200]
				];
				return $mock_response;
			}

			// ADDED: Basic caching for GET requests to reduce API dependency
			$enable_cache = isset($a['enable_cache']) ? $a['enable_cache'] : true;
			$cache_duration = isset($a['cache_duration']) ? $a['cache_duration'] : 300; // 5 minutes default
			
			if ($enable_cache && isset($a['request_method']) && $a['request_method'] === 'GET') {
				$cache_key = 'r4w_api_' . md5(serialize($a));
				$cached_response = get_transient($cache_key);
				if ($cached_response !== false) {
					return $cached_response;
				}
			}

		    $wp_select = "SELECT * from ".$wp_table_app;
		    $r4w_app = $wpdb->get_row($wp_select,ARRAY_A);
		    if(!isset($a['json_return'])){
		    	$json_return = true;
		    }else{
		    	$json_return = $a['json_return'];
		    }
		    // Fixed: Initialize $postfileds variable properly
		    $postfileds = null;
			if(!empty($a['postfileds']['data'])){
				if($a['postfileds']['json_encode']){
					$postfileds = json_encode($a['postfileds']['data']);
				}else{
					$postfileds = $a['postfileds']['data'];
				}
			}
			if($a['auth'] == 'true'){
				$httpdeader = [
			    	"Authorization: Wordpress ".$r4w_app['oauth'],
			    	"APIKEY: ".r4w_api_api_key,
				];
			}
			if($a['auth'] == 'tmp'){
				$httpdeader = [
			    	"Authorization: Wordpress ".$r4w_app['oauth_tmp'],
			    	"APIKEY: ".r4w_api_api_key,
				];
			}
			if($a['auth'] == 'false'){
				$httpdeader = [
					"Authorization: Wordpress ",
					"APIKEY: ".r4w_api_api_key,
				];
			}

			// Added error handling to prevent crashes
			$curl = curl_init();
			if ($curl === false) {
				return array(
					"resp" => null,
					"err" => "Failed to initialize cURL",
					"info" => array("http_code" => 0)
				);
			}

			curl_setopt_array($curl, array(
			  CURLOPT_URL => r4w_api_url_base.r4w_get_version().r4w_api_url_request.$a['url'],
			  CURLOPT_RETURNTRANSFER => true,
			  CURLOPT_ENCODING => "",
			  CURLOPT_MAXREDIRS => 10,
			  CURLOPT_TIMEOUT => 30,  // Fixed: Reduced from 90s to prevent PHP execution timeout
			  CURLOPT_CONNECTTIMEOUT => 10,  // Added connection timeout
			  CURLOPT_SSL_VERIFYPEER => true,  // Fixed: Enable SSL verification for security
			  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			  CURLOPT_CUSTOMREQUEST => $a['request_method'],
			  CURLOPT_REFERER => isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '',
			  CURLOPT_POSTFIELDS => $postfileds,
			  CURLOPT_HTTPHEADER => $httpdeader
			));
			if($json_return === false){
				$return = curl_exec($curl);
				if ($return === false) {
					$return = "cURL Error: " . curl_error($curl);
				}
				curl_close($curl);
				return $return;
			}else{
				$response = curl_exec($curl);
				$err = curl_error($curl);
				$info = curl_getinfo($curl);
				
				// Handle cURL execution failure
				if ($response === false) {
					curl_close($curl);
					return [
						"resp" => null,
						"err" => $err ? $err : "cURL execution failed",
						"info" => $info
					];
				}
				
				$resp = json_decode($response, true);
				curl_close($curl);
				$return = [
					"resp" => $resp,
					"err" => $err,
					"info" => $info
				];
				
				// Cache successful GET responses to reduce API dependency
				if ($enable_cache && isset($a['request_method']) && $a['request_method'] === 'GET' && 
				    $return['info']['http_code'] >= 200 && $return['info']['http_code'] < 300 && empty($return['err'])) {
					set_transient($cache_key, $return, $cache_duration);
				}
				
				return $return;
			}
		}
	}