/**
 * Rank4Win Offline Structure Editor Styles
 * Provides visual styling for the offline editor interface
 */

/* Main Editor Container */
.offline-editor-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Editor Toolbar */
.editor-toolbar {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
    flex-wrap: wrap;
}

.editor-toolbar .btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #ffffff;
    color: #495057;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    text-decoration: none;
}

.editor-toolbar .btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.editor-toolbar .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.editor-toolbar .btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
}

.editor-toolbar .btn-primary:hover:not(:disabled) {
    background: #0056b3;
    border-color: #0056b3;
}

.editor-toolbar .btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.editor-toolbar .btn-secondary:hover:not(:disabled) {
    background: #545b62;
    border-color: #545b62;
}

.editor-toolbar .btn-danger {
    background: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

.editor-toolbar .btn-danger:hover:not(:disabled) {
    background: #c82333;
    border-color: #c82333;
}

.editor-toolbar .btn-outline {
    background: transparent;
    border-color: #6c757d;
    color: #6c757d;
}

.editor-toolbar .btn-outline:hover {
    background: #6c757d;
    color: #ffffff;
}

.editor-toolbar .icon {
    font-weight: bold;
    font-size: 14px;
}

/* Editor Content Area */
.editor-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
    background: #ffffff;
    position: relative;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.7;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #495057;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
    max-width: 300px;
}

/* Tree Structure */
.tree-root, .tree-children {
    list-style: none;
    margin: 0;
    padding: 0;
}

.tree-children {
    margin-left: 24px;
    border-left: 2px solid #e9ecef;
    padding-left: 12px;
}

.tree-node {
    margin: 4px 0;
    position: relative;
}

.tree-node.selected > .node-content {
    background: #e7f3ff;
    border-color: #007bff;
}

.node-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.15s ease;
    min-height: 40px;
}

.node-content:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.toggle-btn, .no-toggle {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
    user-select: none;
    color: #6c757d;
}

.no-toggle {
    cursor: default;
    opacity: 0;
}

.toggle-btn:hover {
    color: #495057;
}

.node-title {
    flex: 1;
    font-weight: 500;
    color: #495057;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.node-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.15s ease;
}

.node-content:hover .node-actions {
    opacity: 1;
}

.node-actions button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 3px;
    background: #f8f9fa;
    color: #6c757d;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
}

.node-actions button:hover {
    background: #e9ecef;
    color: #495057;
}

.node-actions .btn-edit:hover {
    background: #d4edda;
    color: #155724;
}

.node-actions .btn-add:hover {
    background: #d1ecf1;
    color: #0c5460;
}

.node-actions .btn-delete:hover {
    background: #f8d7da;
    color: #721c24;
}

/* Node Properties Panel */
.node-properties {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 300px;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: calc(100vh - 200px);
    overflow: auto;
}

.node-properties h4 {
    margin: 0;
    padding: 16px 16px 0 16px;
    font-size: 16px;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 12px;
    margin-bottom: 16px;
}

.node-properties form {
    padding: 0 16px 16px 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    color: #495057;
    background: #ffffff;
    transition: border-color 0.15s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.form-control::placeholder {
    color: #adb5bd;
}

textarea.form-control {
    resize: vertical;
    min-height: 60px;
}

select.form-control {
    cursor: pointer;
}

.form-actions {
    display: flex;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
    margin-top: 16px;
}

.form-actions .btn {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #ffffff;
    color: #495057;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    text-align: center;
}

.form-actions .btn-success {
    background: #28a745;
    border-color: #28a745;
    color: #ffffff;
}

.form-actions .btn-success:hover {
    background: #218838;
    border-color: #218838;
}

.form-actions .btn-cancel {
    background: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.form-actions .btn-cancel:hover {
    background: #545b62;
    border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .offline-editor-wrapper {
        height: auto;
        min-height: 400px;
    }
    
    .editor-toolbar {
        flex-direction: column;
        gap: 8px;
    }
    
    .editor-toolbar .btn {
        justify-content: center;
    }
    
    .node-properties {
        position: fixed;
        top: 50px;
        left: 16px;
        right: 16px;
        width: auto;
        max-height: calc(100vh - 100px);
    }
    
    .tree-children {
        margin-left: 16px;
        padding-left: 8px;
    }
}

/* Loading Animation for Compatibility */
.save_progress .circular-loader {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Integration with Existing Save Status */
.save_display {
    display: none;
}

.save_display.save_saved {
    color: #28a745;
}

.save_display.save_progress {
    color: #17a2b8;
}

.save_display.save_error {
    color: #dc3545;
}

/* Tree Node Level Indicators */
.tree-node[data-level="0"] > .node-content {
    background: #f8f9fa;
    font-weight: 600;
}

.tree-node[data-level="1"] > .node-content .node-title {
    color: #495057;
}

.tree-node[data-level="2"] > .node-content .node-title {
    color: #6c757d;
}

.tree-node[data-level="3"] > .node-content .node-title {
    color: #868e96;
}

/* Drag and Drop Placeholder (for future enhancement) */
.drop-zone {
    border: 2px dashed #007bff;
    background: #e7f3ff;
    border-radius: 4px;
    padding: 12px;
    margin: 4px 0;
    text-align: center;
    color: #007bff;
    font-size: 12px;
}

/* Focus Styles for Accessibility */
.node-content:focus,
.form-control:focus,
.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}