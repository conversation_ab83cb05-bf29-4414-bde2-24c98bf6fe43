/**
 * Rank4Win Offline Structure Editor
 * Replacement for external AngularJS editor component
 * Provides basic tree structure editing functionality
 */
(function($) {
    'use strict';
    
    // Initialize global editor objects to replace external dependencies
    window.editor = window.editor || {};
    window.apprank4win = window.apprank4win || {};
    
    /**
     * OfflineStructureEditor - Main class for structure editing
     */
    function OfflineStructureEditor() {
        this.currentStructure = null;
        this.selectedNodes = [];
        this.containerId = 'rank4win-cloud-editor';
        this.initialized = false;
        this.isSetup = false;
        this.activationAttempts = 0;
        
        this.init();
    }
    
    OfflineStructureEditor.prototype = {
        
        init: function() {
            if (this.initialized) return;
            
            console.log('🔧 Initializing Offline Structure Editor');
            console.log('🔍 Document ready state:', document.readyState);
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                console.log('⏳ Document still loading, waiting for ready event...');
                $(document).ready(() => {
                    console.log('✅ Document ready event fired');
                    this.detectAndInitialize();
                });
            } else {
                console.log('✅ Document already ready, initializing immediately');
                this.detectAndInitialize();
            }
            
            // Also add a delayed retry in case of timing issues
            setTimeout(() => {
                if (!this.isSetup) {
                    console.log('⚠️ Editor not setup after 2 seconds, retrying...');
                    this.detectAndInitialize();
                }
            }, 2000);
            
            this.initialized = true;
        },
        
        detectAndInitialize: function() {
            // Prevent multiple activations
            this.activationAttempts++;
            if (this.isSetup) {
                console.log('⚠️ Editor already setup, attempt #' + this.activationAttempts + ' - skipping');
                return;
            }
            
            console.log('🔍 Detection attempt #' + this.activationAttempts);
            
            // Add comprehensive debugging
            console.log('🔍 Debug Info:', {
                offline_mode: typeof r4w_editor_vars !== 'undefined' ? r4w_editor_vars.offline_mode : 'undefined',
                R4W_OFFLINE_MODE: typeof R4W_OFFLINE_MODE !== 'undefined' ? R4W_OFFLINE_MODE : 'undefined',
                angular: typeof angular,
                editor_container: $('#rank4win-cloud-editor').length,
                placeholder: $('#offline-editor-placeholder').length,
                jQuery: typeof $,
                document_ready: document.readyState,
                already_setup: this.isSetup
            });
            
            // Check if we're in offline mode
            var isOfflineMode = (typeof r4w_editor_vars !== 'undefined' && r4w_editor_vars.offline_mode === 'true') ||
                               (typeof R4W_OFFLINE_MODE !== 'undefined' && R4W_OFFLINE_MODE === true);
            
            // Also check if external editor dependencies are missing
            var externalEditorMissing = typeof angular === 'undefined' || !window.angular;
            
            console.log('🔍 Mode Detection:', {
                isOfflineMode: isOfflineMode,
                externalEditorMissing: externalEditorMissing,
                shouldActivateOffline: isOfflineMode || externalEditorMissing
            });
            
            if (isOfflineMode || externalEditorMissing) {
                console.log('🔄 Switching to offline editor mode');
                this.activateOfflineMode();
            } else {
                console.log('📡 Online mode detected - external editor should load');
            }
        },
        
        activateOfflineMode: function() {
            console.log('🔧 Activating offline mode...');
            
            // Hide the AngularJS component and show offline placeholder
            console.log('🔍 Before DOM manipulation:', {
                angular_element: $('rank4win-editor').length,
                placeholder_element: $('#offline-editor-placeholder').length,
                main_container: $('#rank4win-cloud-editor').length,
                placeholder_classes: $('#offline-editor-placeholder').attr('class'),
                placeholder_computed_display: $('#offline-editor-placeholder').css('display')
            });
            
            // Hide AngularJS component
            $('rank4win-editor').hide();
            
            // Show offline placeholder using class-based approach
            var placeholder = $('#offline-editor-placeholder');
            placeholder.removeClass('offline-editor-hidden').addClass('offline-editor-visible');
            
            console.log('🔍 After DOM manipulation:', {
                placeholder_visible: placeholder.is(':visible'),
                placeholder_display: placeholder.css('display'),
                placeholder_classes: placeholder.attr('class'),
                placeholder_position: placeholder.css('position'),
                placeholder_zindex: placeholder.css('z-index'),
                placeholder_computed_style: window.getComputedStyle(placeholder[0]).display
            });
            
            // Initialize the offline editor
            console.log('🔧 Setting up container...');
            this.setupContainer();
            console.log('🔧 Binding events...');
            this.bindEvents();
            this.isSetup = true;
            console.log('✅ Offline mode activation complete');
            
            // Final visibility check
            setTimeout(() => {
                console.log('🔍 Final visibility check after 500ms:', {
                    placeholder_visible: placeholder.is(':visible'),
                    placeholder_height: placeholder.height(),
                    placeholder_width: placeholder.width(),
                    wrapper_found: placeholder.find('.offline-editor-wrapper').length,
                    wrapper_visible: placeholder.find('.offline-editor-wrapper').is(':visible')
                });
            }, 500);
        },
        
        setupContainer: function() {
            console.log('🔧 Setting up container...');
            
            var container = $('#offline-editor-placeholder');
            console.log('🔍 Primary container check:', {
                placeholder_found: container.length,
                placeholder_element: container.get(0),
                placeholder_visible: container.is(':visible'),
                placeholder_offset: container.length > 0 ? container.offset() : 'N/A',
                placeholder_dimensions: container.length > 0 ? {width: container.width(), height: container.height()} : 'N/A'
            });
            
            if (container.length === 0) {
                // Fallback to main container if offline placeholder not found
                console.log('⚠️ Offline placeholder not found, using fallback');
                container = $('#' + this.containerId);
                console.log('🔍 Fallback container check:', {
                    containerId: this.containerId,
                    fallback_found: container.length,
                    fallback_element: container.get(0)
                });
            }
            
            if (container.length === 0) {
                console.error('❌ No container found for offline editor!');
                return false;
            }
            
            console.log('🔧 Container found, proceeding with setup');
            
            // Clear any existing content to prevent conflicts
            container.empty();
            
            // Clear any loading placeholders
            $('#ph_str_editor').remove();
            
            // Create the offline editor HTML structure
            var editorHtml = `
                <div class="offline-editor-wrapper">
                    <div class="editor-toolbar">
                        <button id="add-root-node" class="btn btn-primary">
                            <span class="icon">+</span> Add Root Node
                        </button>
                        <button id="add-child-node" class="btn btn-secondary" disabled>
                            <span class="icon">→</span> Add Child
                        </button>
                        <button id="delete-node" class="btn btn-danger" disabled>
                            <span class="icon">×</span> Delete Selected
                        </button>
                        <button id="expand-all" class="btn btn-outline">
                            <span class="icon">⊞</span> Expand All
                        </button>
                        <button id="collapse-all" class="btn btn-outline">
                            <span class="icon">⊟</span> Collapse All
                        </button>
                    </div>
                    
                    <div class="editor-content">
                        <div class="structure-tree" id="structure-tree">
                            <div class="empty-state">
                                <div class="empty-icon">🌳</div>
                                <h3>No Structure Loaded</h3>
                                <p>Create a new structure or load existing data to begin editing.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="node-properties" id="node-properties" style="display: none;">
                        <h4>Node Properties</h4>
                        <form id="node-form">
                            <div class="form-group">
                                <label>Title:</label>
                                <input type="text" id="node-title" class="form-control" placeholder="Enter node title">
                            </div>
                            <div class="form-group">
                                <label>Main Keyword:</label>
                                <input type="text" id="node-main-keyword" class="form-control" placeholder="Primary keyword">
                            </div>
                            <div class="form-group">
                                <label>Secondary Keywords:</label>
                                <textarea id="node-secondary-keywords" class="form-control" rows="2" placeholder="Enter secondary keywords (comma separated)"></textarea>
                            </div>
                            <div class="form-group">
                                <label>Priority:</label>
                                <select id="node-priority" class="form-control">
                                    <option value="1">1 - Highest</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5 - Medium</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9 - Lowest</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Progress:</label>
                                <select id="node-progress" class="form-control">
                                    <option value="1">1 - Not Started</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5 - In Progress</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9 - Complete</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Notes:</label>
                                <textarea id="node-notes" class="form-control" rows="3" placeholder="Additional notes"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="save-node" class="btn btn-success">Save Changes</button>
                                <button type="button" id="cancel-edit" class="btn btn-cancel">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            
            console.log('🔧 Inserting HTML into container...');
            container.html(editorHtml);
            
            console.log('🔍 HTML insertion complete:', {
                container_has_content: container.children().length > 0,
                container_html_length: container.html().length,
                offline_wrapper_found: container.find('.offline-editor-wrapper').length,
                toolbar_found: container.find('.editor-toolbar').length,
                tree_container_found: container.find('#structure-tree').length,
                add_root_button_found: container.find('#add-root-node').length,
                wrapper_visible: container.find('.offline-editor-wrapper').is(':visible'),
                wrapper_dimensions: {
                    width: container.find('.offline-editor-wrapper').width(),
                    height: container.find('.offline-editor-wrapper').height()
                }
            });
            
            // Force visibility on the wrapper
            container.find('.offline-editor-wrapper').css({
                'display': 'flex',
                'min-height': '500px',
                'background': '#ffffff',
                'border': '2px solid #007bff'  // Debug border
            });
            
            console.log('🔧 Forced wrapper visibility styles applied');
            return true;
        },
        
        bindEvents: function() {
            var self = this;
            
            console.log('🔧 Binding events...');
            
            // Check if elements exist before binding
            var elements = {
                add_root: $('#add-root-node').length,
                add_child: $('#add-child-node').length,
                delete_node: $('#delete-node').length,
                expand_all: $('#expand-all').length,
                collapse_all: $('#collapse-all').length,
                save_node: $('#save-node').length,
                cancel_edit: $('#cancel-edit').length
            };
            
            console.log('🔍 Elements to bind:', elements);
            
            // Toolbar events
            $('#add-root-node').on('click', function() { 
                console.log('🔘 Add root node clicked');
                self.addRootNode(); 
            });
            $('#add-child-node').on('click', function() { 
                console.log('🔘 Add child node clicked');
                self.addChildNode(); 
            });
            $('#delete-node').on('click', function() { 
                console.log('🔘 Delete node clicked');
                self.deleteSelectedNode(); 
            });
            $('#expand-all').on('click', function() { 
                console.log('🔘 Expand all clicked');
                self.expandAll(); 
            });
            $('#collapse-all').on('click', function() { 
                console.log('🔘 Collapse all clicked');
                self.collapseAll(); 
            });
            
            // Node form events
            $('#save-node').on('click', function() { 
                console.log('🔘 Save node clicked');
                self.saveNodeProperties(); 
            });
            $('#cancel-edit').on('click', function() { 
                console.log('🔘 Cancel edit clicked');
                self.cancelEdit(); 
            });
            
            console.log('✅ Event binding complete');
            // Tree interaction events will be bound when tree is rendered
        },
        
        addRootNode: function() {
            if (!this.currentStructure) {
                this.currentStructure = {
                    root: {
                        data: {
                            uuid: this.generateUUID(),
                            created: Date.now(),
                            text: "Root Node",
                            r4w_post_data: {
                                keywords: {
                                    main: "",
                                    secondary: []
                                }
                            },
                            priority: 5,
                            progress: 1
                        },
                        children: []
                    }
                };
            }
            
            // Add new root-level node
            var newNode = {
                data: {
                    uuid: this.generateUUID(),
                    created: Date.now(),
                    text: "New Node",
                    r4w_post_data: {
                        keywords: {
                            main: "",
                            secondary: []
                        }
                    },
                    priority: 5,
                    progress: 1
                },
                children: []
            };
            
            this.currentStructure.root.children.push(newNode);
            this.renderTree();
            this.triggerSave();
        },
        
        addChildNode: function() {
            if (this.selectedNodes.length === 0) {
                alert('Please select a parent node first.');
                return;
            }
            
            var parentUuid = this.selectedNodes[0];
            var parentNode = this.findNodeByUuid(this.currentStructure.root, parentUuid);
            
            if (!parentNode) return;
            
            if (!parentNode.children) {
                parentNode.children = [];
            }
            
            var newNode = {
                data: {
                    uuid: this.generateUUID(),
                    created: Date.now(),
                    text: "New Child Node",
                    r4w_post_data: {
                        keywords: {
                            main: "",
                            secondary: []
                        }
                    },
                    priority: 5,
                    progress: 1
                },
                children: []
            };
            
            parentNode.children.push(newNode);
            this.renderTree();
            this.triggerSave();
        },
        
        deleteSelectedNode: function() {
            if (this.selectedNodes.length === 0) {
                alert('Please select a node to delete.');
                return;
            }
            
            if (!confirm('Are you sure you want to delete this node and all its children?')) {
                return;
            }
            
            var nodeUuid = this.selectedNodes[0];
            this.removeNodeByUuid(this.currentStructure.root, nodeUuid);
            this.selectedNodes = [];
            this.renderTree();
            this.hideNodeProperties();
            this.triggerSave();
        },
        
        renderTree: function() {
            var treeContainer = $('#structure-tree');
            
            if (!this.currentStructure || !this.currentStructure.root) {
                treeContainer.html(`
                    <div class="empty-state">
                        <div class="empty-icon">🌳</div>
                        <h3>No Structure Loaded</h3>
                        <p>Create a new structure or load existing data to begin editing.</p>
                    </div>
                `);
                return;
            }
            
            var treeHtml = '<ul class="tree-root">';
            treeHtml += this.renderNode(this.currentStructure.root, 0);
            treeHtml += '</ul>';
            
            treeContainer.html(treeHtml);
            this.bindTreeEvents();
        },
        
        renderNode: function(node, level) {
            var uuid = node.data.uuid;
            var title = node.data.text || 'Untitled Node';
            var hasChildren = node.children && node.children.length > 0;
            var isSelected = this.selectedNodes.includes(uuid);
            
            var html = `
                <li class="tree-node ${isSelected ? 'selected' : ''}" data-uuid="${uuid}" data-level="${level}">
                    <div class="node-content">
                        ${hasChildren ? '<span class="toggle-btn">▼</span>' : '<span class="no-toggle"></span>'}
                        <span class="node-title" title="${title}">${title}</span>
                        <div class="node-actions">
                            <button class="btn-edit" title="Edit Node">✎</button>
                            <button class="btn-add" title="Add Child">+</button>
                            <button class="btn-delete" title="Delete Node">×</button>
                        </div>
                    </div>
            `;
            
            if (hasChildren) {
                html += '<ul class="tree-children">';
                node.children.forEach(child => {
                    html += this.renderNode(child, level + 1);
                });
                html += '</ul>';
            }
            
            html += '</li>';
            return html;
        },
        
        bindTreeEvents: function() {
            var self = this;
            
            // Toggle node expansion
            $('.toggle-btn').off('click').on('click', function(e) {
                e.stopPropagation();
                var li = $(this).closest('li');
                var children = li.find('> .tree-children');
                
                if (children.is(':visible')) {
                    children.hide();
                    $(this).text('▶');
                } else {
                    children.show();
                    $(this).text('▼');
                }
            });
            
            // Node selection
            $('.node-content').off('click').on('click', function(e) {
                if ($(e.target).hasClass('btn-edit') || $(e.target).hasClass('btn-add') || $(e.target).hasClass('btn-delete')) {
                    return;
                }
                
                var uuid = $(this).closest('.tree-node').data('uuid');
                self.selectNode(uuid);
            });
            
            // Node action buttons
            $('.btn-edit').off('click').on('click', function(e) {
                e.stopPropagation();
                var uuid = $(this).closest('.tree-node').data('uuid');
                self.editNode(uuid);
            });
            
            $('.btn-add').off('click').on('click', function(e) {
                e.stopPropagation();
                var uuid = $(this).closest('.tree-node').data('uuid');
                self.selectNode(uuid);
                self.addChildNode();
            });
            
            $('.btn-delete').off('click').on('click', function(e) {
                e.stopPropagation();
                var uuid = $(this).closest('.tree-node').data('uuid');
                self.selectNode(uuid);
                self.deleteSelectedNode();
            });
        },
        
        selectNode: function(uuid) {
            this.selectedNodes = [uuid];
            
            // Update UI
            $('.tree-node').removeClass('selected');
            $(`.tree-node[data-uuid="${uuid}"]`).addClass('selected');
            
            // Update toolbar buttons
            $('#add-child-node').prop('disabled', false);
            $('#delete-node').prop('disabled', false);
            
            // Hide properties panel
            this.hideNodeProperties();
        },
        
        editNode: function(uuid) {
            this.selectNode(uuid);
            var node = this.findNodeByUuid(this.currentStructure.root, uuid);
            
            if (!node) return;
            
            // Populate form
            $('#node-title').val(node.data.text || '');
            $('#node-main-keyword').val(node.data.r4w_post_data?.keywords?.main || '');
            $('#node-secondary-keywords').val(
                (node.data.r4w_post_data?.keywords?.secondary || []).join(', ')
            );
            $('#node-priority').val(node.data.priority || 5);
            $('#node-progress').val(node.data.progress || 1);
            $('#node-notes').val(node.data.note || '');
            
            // Show properties panel
            $('#node-properties').show().attr('data-editing-uuid', uuid);
        },
        
        saveNodeProperties: function() {
            var uuid = $('#node-properties').attr('data-editing-uuid');
            var node = this.findNodeByUuid(this.currentStructure.root, uuid);
            
            if (!node) return;
            
            // Update node data
            node.data.text = $('#node-title').val() || 'Untitled Node';
            node.data.r4w_post_data = node.data.r4w_post_data || { keywords: {} };
            node.data.r4w_post_data.keywords.main = $('#node-main-keyword').val();
            node.data.r4w_post_data.keywords.secondary = 
                $('#node-secondary-keywords').val().split(',').map(k => k.trim()).filter(k => k);
            node.data.priority = parseInt($('#node-priority').val()) || 5;
            node.data.progress = parseInt($('#node-progress').val()) || 1;
            node.data.note = $('#node-notes').val();
            
            // Re-render tree and hide properties
            this.renderTree();
            this.hideNodeProperties();
            this.triggerSave();
        },
        
        cancelEdit: function() {
            this.hideNodeProperties();
        },
        
        hideNodeProperties: function() {
            $('#node-properties').hide().removeAttr('data-editing-uuid');
        },
        
        expandAll: function() {
            $('.tree-children').show();
            $('.toggle-btn').text('▼');
        },
        
        collapseAll: function() {
            $('.tree-children').hide();
            $('.toggle-btn').text('▶');
        },
        
        // API compatibility methods
        importJson: function(structureData) {
            console.log('📥 Importing structure data:', structureData);
            this.currentStructure = structureData;
            this.renderTree();
        },
        
        exportJson: function() {
            console.log('📤 Exporting structure data:', this.currentStructure);
            return this.currentStructure || { root: { data: {}, children: [] } };
        },
        
        getSelectedNodes: function() {
            return this.selectedNodes.slice(); // Return copy
        },
        
        select: function(nodeUuids, replace) {
            if (replace) {
                this.selectedNodes = [];
            }
            
            if (Array.isArray(nodeUuids)) {
                this.selectedNodes = this.selectedNodes.concat(nodeUuids);
            } else if (nodeUuids) {
                this.selectedNodes.push(nodeUuids);
            }
            
            // Update UI
            $('.tree-node').removeClass('selected');
            this.selectedNodes.forEach(uuid => {
                $(`.tree-node[data-uuid="${uuid}"]`).addClass('selected');
            });
        },
        
        fire: function(eventName) {
            console.log('🔥 Event fired:', eventName);
            // Stub for event system compatibility
        },
        
        // Utility methods
        findNodeByUuid: function(node, targetUuid) {
            if (node.data.uuid === targetUuid) {
                return node;
            }
            
            if (node.children) {
                for (var i = 0; i < node.children.length; i++) {
                    var found = this.findNodeByUuid(node.children[i], targetUuid);
                    if (found) return found;
                }
            }
            
            return null;
        },
        
        removeNodeByUuid: function(parentNode, targetUuid) {
            if (!parentNode.children) return false;
            
            for (var i = 0; i < parentNode.children.length; i++) {
                if (parentNode.children[i].data.uuid === targetUuid) {
                    parentNode.children.splice(i, 1);
                    return true;
                }
                
                if (this.removeNodeByUuid(parentNode.children[i], targetUuid)) {
                    return true;
                }
            }
            
            return false;
        },
        
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0;
                var v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },
        
        triggerSave: function() {
            // Trigger save status update
            $('.save_display').hide();
            $('.save_progress').show();
            
            setTimeout(() => {
                $('.save_display').hide();
                $('.save_saved').show();
            }, 1000);
        }
    };
    
    // Create global instances for compatibility
    var offlineEditor = new OfflineStructureEditor();
    
    // Map to external API expectations
    window.editor.apprank4win = offlineEditor;
    window.apprank4win = {
        getSelectedNodes: function() { return offlineEditor.getSelectedNodes(); },
        select: function(nodes, replace) { return offlineEditor.select(nodes, replace); },
        fire: function(event) { return offlineEditor.fire(event); }
    };
    
    // Manual trigger for debugging
    window.r4w_force_offline_editor = function() {
        console.log('🔧 Manual trigger activated');
        offlineEditor.detectAndInitialize();
        return offlineEditor;
    };
    
    // Status report for debugging
    window.r4w_debug_status = function() {
        console.log('📊 Offline Editor Debug Status Report:');
        console.log('🔍 Editor State:', {
            initialized: offlineEditor.initialized,
            isSetup: offlineEditor.isSetup,
            activationAttempts: offlineEditor.activationAttempts,
            currentStructure: offlineEditor.currentStructure,
            selectedNodes: offlineEditor.selectedNodes
        });
        
        var placeholder = $('#offline-editor-placeholder');
        var wrapper = placeholder.find('.offline-editor-wrapper');
        
        console.log('🔍 DOM Elements:', {
            main_container: $('#rank4win-cloud-editor').length,
            placeholder: placeholder.length,
            placeholder_visible: placeholder.is(':visible'),
            placeholder_classes: placeholder.attr('class'),
            placeholder_computed_display: placeholder.length > 0 ? window.getComputedStyle(placeholder[0]).display : 'N/A',
            angular_element: $('rank4win-editor').length,
            offline_wrapper: wrapper.length,
            wrapper_visible: wrapper.is(':visible'),
            wrapper_dimensions: wrapper.length > 0 ? {width: wrapper.width(), height: wrapper.height()} : 'N/A',
            toolbar_buttons: $('.editor-toolbar .btn').length,
            structure_tree: $('#structure-tree').length
        });
        console.log('🔍 Environment:', {
            jQuery: typeof $,
            angular: typeof angular,
            offline_mode_vars: typeof r4w_editor_vars !== 'undefined' ? r4w_editor_vars.offline_mode : 'undefined',
            R4W_OFFLINE_MODE: typeof R4W_OFFLINE_MODE !== 'undefined' ? R4W_OFFLINE_MODE : 'undefined',
            document_ready: document.readyState
        });
        console.log('🔍 Page URL:', window.location.href);
        console.log('🔍 CSS Check:', {
            offline_editor_css_loaded: $('link[href*="lib.offline-editor.css"]').length > 0
        });
    };
    
    // Force visibility function for debugging
    window.r4w_force_visibility = function() {
        console.log('🔧 Force visibility activated');
        var placeholder = $('#offline-editor-placeholder');
        var wrapper = placeholder.find('.offline-editor-wrapper');
        
        placeholder.removeClass('offline-editor-hidden').addClass('offline-editor-visible');
        placeholder.css({'display': 'block !important', 'visibility': 'visible'});
        
        wrapper.css({
            'display': 'flex !important',
            'min-height': '500px',
            'background': '#ffffff',
            'border': '3px solid #ff0000',  // Red debug border
            'z-index': '9999'
        });
        
        console.log('🔧 Force visibility applied - look for red border');
        return {placeholder: placeholder.length, wrapper: wrapper.length};
    };
    
    // Global reference for debugging
    window.r4w_offline_editor = offlineEditor;
    
    console.log('🚀 Offline Structure Editor initialized successfully!');
    console.log('💡 Debug functions available:');
    console.log('  - r4w_debug_status() - Complete diagnostic report');
    console.log('  - r4w_force_offline_editor() - Manual initialization');
    console.log('  - r4w_force_visibility() - Force editor to be visible with red border');
    
})(jQuery);