!function(r){"use strict";var e={itemWidth:null,skin:"",multi:!1,active:"selected",full:!1,colNum:null,dataKey:"ui-choose",change:null,click:null};function s(t,i){return this.el=t,this._tag=this.el.prop("tagName").toLowerCase(),this._opt=r.extend({},e,i),this._init()}r.fn.ui_choose=function(e){var t=r(this),i=t.length;if(1===i)return new s(t,e);1<i&&t.each(function(t,i){new s(r(i),e)})},s.prototype={_init:function(){var t=this.el.data(this._opt.dataKey);if(t)return t;this.el.data(this._opt.dataKey,this),"select"==this._tag?this.multi=this.el.prop("multiple"):this.multi=this.el.attr("multiple")?!!this.el.attr("multiple"):this._opt.multi;var i=this["_setHtml_"+this._tag];i&&i.call(this),this._opt.full&&this._wrap.addClass("choose-flex"),this._wrap.addClass(this._opt.skin),this.multi&&!this._opt.skin&&this._wrap.addClass("choose-type-right"),this._bindEvent()},_setHtml_ul:function(){this._wrap=this.el,this._items=this.el.children("li"),this._opt.itemWidth&&this._items.css("width",this._opt.itemWidth)},_setHtml_select:function(){var n='<ul class="ui-choose">';this.el.find("option").each(function(t,i){var e=r(i),s=e.text(),l=e.prop("value"),h=e.prop("selected")?"selected":"",a=e.prop("disabled")?" disabled":"";n+='<li title="'+s+'" data-value="'+l+'" class="'+h+a+'">'+s+"</li> "}),n+="</ul>",this.el.after(n),this._wrap=this.el.next("ul.ui-choose"),this._items=this._wrap.children("li"),this._opt.itemWidth&&this._items.css("width",this._opt.itemWidth),this.el.hide()},_bindEvent:function(){var h=this;return h._wrap.on("click","li",function(){var t=r(this);if(!t.hasClass("disabled"))if(h.multi){t.toggleClass(h._opt.active);l=[];h._items.each(function(t,i){var e=r(this);if(e.hasClass(h._opt.active)){var s="select"==h._tag?e.attr("data-value"):e.index();l.push(s)}}),h.val(l),h._triggerClick(l,t)}else{var l=t.attr("data-value")||t.index();h.val(l),h._triggerClick(l,t)}}),h},_triggerChange:function(t,i){i=i||this._wrap,this.change(t,i),"function"==typeof this._opt.change&&this._opt.change.call(this,t,i)},_triggerClick:function(t,i){this.click(t,i),"function"==typeof this._opt.click&&this._opt.click.call(this,t,i)},_val_select:function(t){if(0===arguments.length)return this.el.val();var i=this.el.val();if(this.multi){if(null==t||""==t||t==[])this.el.val(null),this._items.removeClass(this._opt.active);else for(var e in t="object"==typeof t?t:[t],this.el.val(t),this._items.removeClass(this._opt.active),t){var s=t[e];this._wrap.children('li[data-value="'+s+'"]').addClass(this._opt.active)}t!==i&&this._triggerChange(t)}else{var l=this._wrap.children('li[data-value="'+t+'"]');if(!l.length)return this;this.el.val(t),l.addClass(this._opt.active).siblings("li").removeClass(this._opt.active),t!==i&&this._triggerChange(t)}return this},_val_ul:function(t){if(0===arguments.length){var i=this._wrap.children("li."+this._opt.active);if(this.multi){if(0==i.length)return null;var s=this,l=[];return i.each(function(t,i){var e=r(i);e.hasClass(s._opt.active)&&l.push(e.index())}),l}return-1==i.index()?null:i.index()}var e=this._val_ul();if(this.multi){if(null==t||""==t||t==[])this._items.removeClass(this._opt.active);else for(var h in t="object"==typeof t?t:[t],this._items.removeClass(this._opt.active),t){var a=t[h];this._wrap.children("li").eq(a).addClass(this._opt.active)}t!==e&&this._triggerChange(t)}else{var n=this._wrap.children("li").eq(t);if(!n.length)return this;n.addClass(this._opt.active).siblings("li").removeClass(this._opt.active),t!==e&&this._triggerChange(t,n)}return this},val:function(){return this["_val_"+this._tag].apply(this,arguments)},change:function(t,i){},click:function(t,i){},hide:function(){return this._wrap.hide(),this},show:function(){return this._wrap.show(),this},selectAll:function(){if(!this.multi)return this;if("select"==this._tag){this.el.find("option").not(":disabled").prop("selected",!0);var e=this.el.val();this.val(e)}else{e=[];this._items.not(".disabled").each(function(t,i){e.push(t)}),this.val(e)}return this}}}(jQuery);