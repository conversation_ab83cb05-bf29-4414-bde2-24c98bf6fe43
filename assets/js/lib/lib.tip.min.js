!function(n,o){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=n.document?o(n,!0):function(t){if(!t.document)throw new Error("Tip requires a window with a document");return o(t)}:"function"==typeof define&&define.cmd?define(function(t,e,i){i.exports=o(n,t("jquery"))}):"function"==typeof define&&define.amd?define("tip",["jquery"],o(n,$)):o(n,jQuery)}("undefined"!=typeof window?window:this,function(t,p,e){"use strict";var i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"},r=-1,n="<script[^>]*>([\\S\\s]*?)</script\\s*>",o="tip-hidden";function s(t){return(e=t=""+t,e.replace(new RegExp(n,"img"),"")).replace(/[\r\t\n]/g," ").replace(/[&<>"'\/`]/g,function(t){return i[t]});var e}function a(t,i){return i=""+i,p.each(t,function(t,e){i=i.replace(new RegExp("{"+t+"}","ig"),s(e).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&#x2F;/g,"/").replace(/&#x60;/g,"`"))}),i}var c=function(t){return this.attributes={},this.target=null,this.wrap=null,this.content=null,this.set(c.defaults),p.isPlainObject(t)&&this.init(t),this};return c.defaults={position:"",TIP_WRAP:'<div class="tip-wrap {position} '+o+'" id="tip-{id}"></div>',TIP_CONTENT:'<div class="tip-content">{content}</div>'},c.prototype={version:"0.1.0",constructor:c,set:function(t){return p.isPlainObject(t)&&p.extend(this.attributes,t),this},init:function(t){return this.set(t)._init().render().attachEvents(),this},_init:function(){var t,e=this.attributes,i=p(e.target),n=e.position||i.attr("data-position")||"bottom-center",o={id:(r+=1,t?t+"-"+r:r)};return this.target=i,o.position=n?"tip-"+n.toLocaleLowerCase()+"-wrap":"tip-bottom-center-wrap",this.wrap=p(a(o,e.TIP_WRAP)),this.content=p(a({content:e.tip||i.attr("data-tip")||i.attr("title")},e.TIP_CONTENT)),this},render:function(){return p(document.body).append(this.wrap.append(this.content)),this},show:function(){return this.wrap.removeClass(o),this.resize().updatePosition(),this},hide:function(){return this.wrap.off().addClass(o),this},resize:function(){return this.wrap.width(this.content[0].offsetWidth).height(this.content[0].offsetHeight),this},updatePosition:function(){var t,e=p(this.target),i=e[0].offsetHeight,n=e[0].offsetWidth,o=this.wrap,r=o.height(),s=o.width(),a=e.offset().left,c=e.offset().top;switch((this.attributes.position||e.attr("data-position")||"bottom-center").toLocaleLowerCase()){case"top-left":t={left:a,top:c-r-8};break;case"top":case"top-center":t={left:a+(n-s)/2,top:c-r-8};break;case"top-right":t={left:a+(n-s),top:c-r-8};break;case"right":t={left:a+n+8,top:c+(i-r)/2};break;case"bottom-left":t={left:a,top:c+i+8};break;case"bottom":case"bottom-center":t={left:a+(n-s)/2,top:c+i+8};break;case"bottom-right":t={left:a+(n-s),top:c+i+8};break;case"left":t={left:a-s-8,top:c+(i-r)/2};break;default:t={left:a+(n-s)/2,top:c+i+8}}return o.css(t),this},attachEvents:function(){var e=this,t=this.target;return t.on("mouseenter",function(t){e.show(),t.stopPropagation(),t.preventDefault()}),t.on("mouseleave",function(){e.hide()}),this}},p.fn.extend({tip:function(i){var n=[];return p(this).each(function(t,e){p.isPlainObject(i)||(i={}),i.target=e,n.push(new c(i))}),n}}),e||(t.Tip=c),c});