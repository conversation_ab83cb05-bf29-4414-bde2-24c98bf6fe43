/**
 * SimpleNotificator - jQuery friendly plugin
 *
 * Created by <PERSON><PERSON> on 20/01/2016.
 */
var simpleNotificator=function(){var e,a,c,r;e=jQuery,a={inAnimationEffect:{opacity:1},inAnimationDuration:600,outAnimationEffect:{opacity:0},outAnimationDuration:600,outDelay:2e3,hoverDelay:1e3,position:"bottom",type:"info",textPrefix:"Pay attention!",stickMode:!1,width:"100%",callback:null};var f=function(i,t){if(i.animate(t.inAnimationEffect,t.inAnimationDuration,function(){t.stickMode||(i.on("mouseenter",n(i)),i.on("mouseleave",o(i,t)))}),!c.stickMode)return u(i,t)},u=function(i,t){i.delay(t.outDelay).animate(t.outAnimationEffect,t.outAnimationDuration,function(){t.callback&&t.callback.call(this),e(this).remove()})},n=function(i){return function(){return i.stop(!0,!0)}},o=function(i,t){return function(){return i.delay(t.hoverDelay).queue(u(i,t))}},t=function(i){r=i};return{initialize:function(i){return r=e('<div class="notificatorWrapper"></div>'),t(r),i.append(r)},createNotification:function(i,t,n){c=t?e.extend({},a,t):a;var o=e('<div style="width:'+c.width+'" class="simpleNotification simpleNotification-'+c.type+'"><strong>'+c.textPrefix+" </strong>"+i+"</div>");switch(c.stickMode&&e('<button class="dismiss">x</button>').prependTo(o).click(function(){return c.outDelay=0,u(o,c)}),c.position){case"bottom":r.append(o);break;case"top":r.prepend(o);break;default:r.append(o)}return f(o,c)},setWrapper:t}};!function(o){var e=new simpleNotificator;function a(i,t,n){return i.type=t,i.textPrefix||(i.textPrefix=n),i}o.fn.notification=function(i,t,n){return 0==o(this).find(".notificatorWrapper").length?e.initialize(o(this)):e.setWrapper(o(this).children(".notificatorWrapper")),o.isFunction(t)?t={callback:n=t}:o.isFunction(n)&&(t.callback=n),e.createNotification(i,t),this},o.fn.successNotification=function(i,t,n){o.isFunction(t)&&(n=t,t={}),o.fn.notification.call(this,i,a(t,"success","Well done!"),n)},o.fn.warningNotification=function(i,t,n){o.isFunction(t)&&(n=t,t={}),o.fn.notification.call(this,i,a(t,"warning","Warning!"),n)},o.fn.dangerNotification=function(i,t,n){o.isFunction(t)&&(n=t,t={}),o.fn.notification.call(this,i,a(t,"danger","Error!"),n)}}(jQuery);