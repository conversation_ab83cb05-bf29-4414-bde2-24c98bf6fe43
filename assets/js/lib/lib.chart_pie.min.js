window.r4w_chartColors = {
	any: '#ced4da',
	poor: '#fc5252',
	mediocre: '#ff7f00',
	good: '#8bc34a',
	perfect: '#4fae33',
	index: '#00BCD4',
	noindex: '#90edf9',
	follow: '#ffc107',
	nofollow: '#ffe69c',
};
Chart.defaults.global.tooltips.custom = function(tooltip) {
	if(tooltipEl){
		var name = jQuery(this._chart.canvas).attr('data-name');
		var id = jQuery(this._chart.canvas).attr('id');
		var tooltipEl = document.getElementById(id+'-tooltip');
		if (tooltip.opacity === 0) {
			tooltipEl.style.opacity = 0;
			return;
		}
		tooltipEl.classList.remove('above', 'below', 'no-transform');
		if (tooltip.yAlign) {
			tooltipEl.classList.add(tooltip.yAlign);
		} else {
			tooltipEl.classList.add('no-transform');
		}	
		function getBody(bodyItem) {
			return bodyItem.lines;
		}
		if (tooltip.body) {
			var bodyLines = tooltip.body.map(getBody);
			var innerHtml = '';
			bodyLines.forEach(function(body, i) {
				var myarr = body[0].split(":");
				var colors = tooltip.labelColors[i];
				var style = 'background:' + colors.backgroundColor;
				style += '; border-color:' + colors.borderColor;
				style += '; border-width: 2px';
				var span = '<span class="chartjs-tooltip-key" style="' + style + '"></span>';
				innerHtml += '<tr><td>' + span + myarr[1] + ' '+ name +' </td></tr>';
			});
			innerHtml += '</tbody>';
			var tableRoot = tooltipEl.querySelector('table');
			tableRoot.innerHTML = innerHtml;
		}
		var positionY = this._chart.canvas.offsetTop;
		var positionX = this._chart.canvas.offsetLeft;
		tooltipEl.style.opacity = 1;
		tooltipEl.style.left = positionX + tooltip.caretX + 'px';
		tooltipEl.style.top = positionY + tooltip.caretY + 'px';
		tooltipEl.style.fontFamily = tooltip._bodyFontFamily;
		tooltipEl.style.fontSize = tooltip.bodyFontSize;
		tooltipEl.style.fontStyle = tooltip._bodyFontStyle;
		tooltipEl.style.padding = tooltip.yPadding + 'px ' + tooltip.xPadding + 'px';
	}
};
function r4w_overscore_config(a,b,c,d,e){
	var r4w_config = {
		type: 'pie',
		data: {
			datasets: [{
				data: [a, b, c, d, e],
				backgroundColor: [
					window.r4w_chartColors.any,
					window.r4w_chartColors.poor,
					window.r4w_chartColors.mediocre,
					window.r4w_chartColors.good,
					window.r4w_chartColors.perfect,
				],
			}],
			labels: [
				'any',
				'poor',
				'mediocre',
				'good',
				'perfect',
			]
		},
		options: {
			responsive: false,
			legend: { display: false },
			tooltips: { enabled: false }
		}
	};
	return r4w_config;
}
function old_r4w_indexability_config(a,b,c,d){
	var r4w_config = {
		type: 'pie',
		data: {
			datasets: [{
				data: [a, b, c, d],
				backgroundColor: [
					window.r4w_chartColors.index,
					window.r4w_chartColors.noindex,
					window.r4w_chartColors.follow,
					window.r4w_chartColors.nofollow,
				],
			}],
			labels: [
				'index',
				'noindex',
				'follow',
				'nofollow'
			]
		},
		options: {
			responsive: false,
			legend: { display: true },
			tooltips: { enabled: false }
		}
	};
	return r4w_config;
}
function r4w_indexability_config(a,b,c,d){
	var r4w_config = {
		type:'PolarArea',
		data: {
			datasets: [{
				data: [a, b, c, d],
				backgroundColor: [
					window.r4w_chartColors.index,
					window.r4w_chartColors.noindex,
					window.r4w_chartColors.follow,
					window.r4w_chartColors.nofollow,
				]
			}],
			labels: [
				'index',
				'noindex',
				'follow',
				'nofollow'
			]
		},
		options: {
			responsive: false,
			legend: { display: true },
			tooltips: { enabled: false }
		}
	};
	return r4w_config;
}