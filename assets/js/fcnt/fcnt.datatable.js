var config_datatable = { language: { processing: localize_fcnt_datatable.datatable_processing+"...", search: localize_fcnt_datatable.datatable_search+" :", lengthMenu: localize_fcnt_datatable.datatable_lengthMenu, info: localize_fcnt_datatable.datatable_info, infoEmpty: localize_fcnt_datatable.datatable_infoEmpty, infoFiltered: localize_fcnt_datatable.datatable_infoFiltered, infoPostFix: localize_fcnt_datatable.datatable_infoPostFix, loadingRecords: localize_fcnt_datatable.datatable_loadingRecords+"...", zeroRecords: localize_fcnt_datatable.datatable_zeroRecords, emptyTable: localize_fcnt_datatable.datatable_emptyTable, paginate: { first: localize_fcnt_datatable.datatable_paginate_first, previous: localize_fcnt_datatable.datatable_paginate_previous, next: localize_fcnt_datatable.datatable_paginate_next, last: localize_fcnt_datatable.datatable_paginate_last }, aria: { sortAscending: ": "+localize_fcnt_datatable.datatable_aria_sortAscending, sortDescending: ": "+localize_fcnt_datatable.datatable_aria_sortDescending } }, "order": [[ 1, "desc" ]], "searching": false, "paging": true, "pageLength": 10, "info": true, "lengthChange":true }; 