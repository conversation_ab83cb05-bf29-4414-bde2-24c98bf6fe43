# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Rank4Win is a WordPress SEO plugin written in PHP that provides semantic SEO tools and keyword strategy features. The plugin helps website owners optimize their content for search engines through keyword analysis, content optimization, and semantic structure building.

## Architecture

### Core Structure
- **Main plugin file**: `rank4win.php` - Entry point that loads all components
- **Configuration**: `_inc/config.php` - Defines constants, API endpoints, and database table names  
- **Classes**: `_inc/class/` - Core functionality classes (app, template engine, async processing, etc.)
- **Functions**: `_inc/fcnt/` - Feature-specific functions organized by functionality
- **Templates**: `tpl/` - UI template files organized by feature areas
- **Assets**: `assets/` - CSS, JavaScript, images, and SVG icons

### Key Components
- **Template System**: Custom template engine (`class.tpl.php`) with placeholder replacement using `[@key]` syntax
- **Database Integration**: Uses WordPress database with custom tables (rank4win_app, rank4win_document, etc.)
- **API Integration**: Connects to external service at `https://ws.r4w.fr/` for SEO analysis
- **Async Processing**: Background task processing (`class.async.php`, `class.process.php`)
- **Multi-language Support**: Translation files in `languages/` directory

### Template Organization
Templates are organized by feature areas:
- `account/` - User account management
- `analysis/` - SEO analysis tools  
- `settings/` - Plugin configuration
- `tools/` - Various SEO tools
- `wizard/` - Setup wizard
- `box_*` - Individual content analysis components

## Development Commands

This is a WordPress plugin with no build system. No npm, composer, or build commands are required.

## Plugin Structure

- Auto-loads all files matching patterns: `fcnt.*.php`, `class.*.php`, `lib.init.php`
- Uses WordPress hooks and filters for integration
- Database tables are created/managed through health check system
- Templates use custom syntax with `[@variable]` placeholders

## Key Constants

- `r4w_api_url_base`: "https://ws.r4w.fr/"
- `r4w_api_api_key`: API key for external service integration
- Database table prefixes: `rank4win_*`

## WordPress Integration

- Text domain: `app_rank4win`
- Supports WordPress multisite
- Uses WordPress database abstraction
- Integrates with WordPress admin interface