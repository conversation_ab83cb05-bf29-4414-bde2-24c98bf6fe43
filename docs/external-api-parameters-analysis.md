# External API Parameters Analysis - Rank4Win Plugin

## Overview

The Rank4Win WordPress plugin sends comprehensive data to an external service at `https://ws.r4w.fr/` for SEO analysis and processing. This document details all parameters and data types transmitted to the external service.

## API Configuration

- **Base URL**: `https://ws.r4w.fr/`
- **API Path**: `/api`
- **API Key**: `572335d3-dd0f-4550-81cd-ec8ff4807e6f` (hardcoded in `_inc/config.php`)
- **Version**: Dynamic, retrieved via `r4w_get_version()` function from plugin metadata

## Request Infrastructure

### cURL Function
All API requests use the centralized `r4w_curl_request($a)` function in `_inc/fcnt/fcnt.curl_request.php`.

### Standard Headers
```php
"Authorization: Wordpress {oauth_token}"  // oauth, oauth_tmp, or empty
"APIKEY: {api_key}"
```

### cURL Options
- `CURLOPT_REFERER`: `$_SERVER['SERVER_NAME']`
- `CURLOPT_SSL_VERIFYPEER`: 0 (disabled)
- `CURLOPT_TIMEOUT`: 90 seconds
- `CURLOPT_HTTP_VERSION`: CURL_HTTP_VERSION_1_1

## Data Categories Sent to External Service

### 1. Authentication & User Management
**Endpoints**: `/wp/user/auth/`, `/wp/user/register/`, `/wp/user/forgot/`, `/wp/oauth/`

**Parameters**:
- User credentials (email, password)
- OTP verification codes
- WordPress OAuth tokens (`oauth`, `oauth_tmp`)
- Registration data (name, email, password, confirmation)
- Forgot password requests

### 2. Document & Content Management
**Endpoints**: `/wp/create/document/`, `/wp/cloud/document/{uuid}`, `/wp/create/document/{uuid}`

**Parameters**:
```php
"uuid" => $document_uuid                    // Document identifier
"wp_post_id" => $post_id                   // WordPress post ID
"keywords_main" => $keywords               // Primary keywords
"title" => $wp_title                       // Page title (hex-encoded)
"content" => $wp_content                   // Page content (hex-encoded)
"data" => $wp_data                         // Additional document data
"update" => $update_flag                   // Update indicator
"deploy_data" => $deploy_data              // Deployment configuration
```

### 3. Keywords & SEO Data
**Endpoints**: `/wp/keywords/{type}/{uuid}`, `/wp/autocomplete/`

**Parameters**:
- Main keywords
- Secondary keywords
- Lexical keywords
- Keyword autocomplete queries
- Semantic structures
- SEO optimization data

### 4. Analysis & Research
**Endpoints**: `/wp/analysis/website/`, `/wp/analysis/research/`, `/wp/analysis/competition/`

**Parameters**:
- Website URLs for analysis
- Competitor domains and URLs
- Research queries and parameters
- Website performance metrics
- SEO audit requests

### 5. Semantic Structure Management
**Endpoints**: `/wp/structure/semantic/`, `/wp/structure/semantic/{uuid}`, `/wp/structure/deploy/`

**Parameters**:
```php
"semantic_uuid" => $uuid                   // Semantic structure ID
"semantic_data" => $structure_data         // Structure configuration
"deploy_uuid" => $deploy_id               // Deployment identifier
"structure_config" => $config             // Structure settings
```

### 6. Configuration & Settings
**Endpoints**: `/wp/config/`, `/wp/user/feature/`

**Parameters**:
- WordPress site configuration
- Plugin settings and preferences
- User feature toggles
- Language and localization settings
- System configuration data

### 7. Cloud Services
**Endpoints**: `/wp/cloud/answers/{doc_id}`, `/wp/cloud/semantics/{doc_id}`, `/wp/cloud/synonymous/{doc_id}`

**Parameters**:
- Document analysis requests
- Semantic analysis data
- Synonymous word requests
- Content optimization suggestions

### 8. Business & Billing
**Endpoints**: `/wp/stripe/subscription/`, `/wp/products/{uuid}`, `/wp/user/subscription/`, `/wp/buy/option/`

**Parameters**:
```php
"product_uuid" => $product_id             // Product identifier
"subscription_data" => $sub_info          // Subscription details
"payment_method" => $payment_info         // Stripe payment data
"customer_data" => $customer_info         // Customer information
"invoice_data" => $invoice_info           // Billing information
```

### 9. Strategy Management
**Endpoints**: `/wp/strategy/`

**Parameters**:
- SEO strategy configurations
- Content strategy data
- Optimization plans
- Performance targets

### 10. System & Health Checks
**Endpoints**: `/wp/ping/system/`, `/wp/account/`

**Parameters**:
- System health data
- Account verification requests
- Service availability checks
- Plugin status information

## Key API Endpoints (60+ total)

### Authentication Endpoints
- `/wp/user/auth/` - User login
- `/wp/user/register/` - User registration
- `/wp/user/forgot/` - Password recovery
- `/wp/user/email/otp/` - Email OTP verification
- `/wp/user/password/otp/` - Password OTP verification
- `/wp/oauth/` - WordPress OAuth
- `/wp/association/` - WordPress association

### Content Management
- `/wp/create/document/` - Create new document
- `/wp/create/document/{uuid}` - Update document
- `/wp/cloud/document/{uuid}` - Cloud document operations

### Analysis & Research
- `/wp/analysis/website/` - Website analysis
- `/wp/analysis/research/` - Keyword research
- `/wp/analysis/competition/` - Competitor analysis
- `/wp/keywords/{type}/{uuid}` - Keyword management
- `/wp/autocomplete/` - Keyword suggestions

### Structure & Strategy
- `/wp/structure/semantic/` - Semantic structure management
- `/wp/structure/semantic/{uuid}` - Specific structure operations
- `/wp/structure/deploy/` - Structure deployment
- `/wp/strategy/` - Strategy management

### Business Operations
- `/wp/stripe/subscription/` - Subscription management
- `/wp/stripe/customer/` - Customer management
- `/wp/products/{uuid}` - Product information
- `/wp/buy/option/` - Purchase options
- `/wp/user/subscription/` - User subscriptions
- `/wp/user/invoice/` - Invoice management

### Configuration & Settings
- `/wp/config/` - System configuration
- `/wp/user/feature/` - Feature management
- `/wp/account/` - Account information
- `/wp/required/` - Required settings
- `/wp/support/` - Support requests

## Security Considerations

1. **API Key Exposure**: Hardcoded API key in configuration file
2. **SSL Verification Disabled**: `CURLOPT_SSL_VERIFYPEER` set to 0
3. **Sensitive Data Transmission**: User credentials, content, and billing data sent to external service
4. **OAuth Tokens**: WordPress authentication tokens transmitted
5. **Content Encoding**: Some content hex-encoded but not encrypted

## Data Flow Summary

1. **WordPress Content** → Hex-encoded → External service for SEO analysis
2. **User Authentication** → OAuth tokens → External service validation  
3. **SEO Data** → JSON-encoded parameters → External analysis engine
4. **Configuration** → Site settings → External service optimization
5. **Billing Data** → Stripe integration → External payment processing

## Files Involved

- `_inc/fcnt/fcnt.curl_request.php` - Main cURL function
- `_inc/config.php` - API configuration constants
- `_inc/class/class.execjs.php` - Primary API interaction class
- `tpl/*/` - Template files with API calls
- `_inc/class/class.app.php` - Application-level API calls

This analysis was conducted by examining all instances of `r4w_curl_request()` function calls throughout the codebase and documenting the parameters passed to each endpoint.