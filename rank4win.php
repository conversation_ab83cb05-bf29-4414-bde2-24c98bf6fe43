<?php
/*
 * Plugin Name:  Rank4win
 * Plugin URI:   https://rank4win.fr
 * Description:  Rank4Win is an ultra powerful new generation SEO tool & helps you multiply your traffic. All you need to reach the first page of Google! We created Rank4win, a WordPress SEO plugin, to make it easier for every website owner to build keyword strategy, to create a Semantic Structure to reinforce the natrual SEO of their site, to have help with Optimizing Content Writing and Enrichment lexicalment and semantically.
 * Version:      1.3.7
 * Author:       Rank4win
 * Author URI:   https://rank4win.fr
 * Text Domain:  app_rank4win
 * Domain Path:  /languages/
*/ 

    /**
     *  Quitter si on y accède directement
     */
        if ( ! defined( 'ABSPATH' ) ) {
            exit;
        }

    /**
     * Check PHP version compatibility before loading plugin
     * FIXED: Added PHP version check to prevent crashes on older PHP versions
     */
        if ( version_compare( PHP_VERSION, '5.6', '<' ) ) {
            add_action( 'admin_notices', function() {
                echo '<div class="error"><p><strong>Rank4Win Error:</strong> This plugin requires PHP 5.6 or higher. You are running PHP ' . PHP_VERSION . '. Please upgrade your PHP version.</p></div>';
            });
            return; // Stop loading the plugin
        }

    /**
     * Check WordPress version compatibility
     */
        if ( ! function_exists( 'get_bloginfo' ) || version_compare( get_bloginfo( 'version' ), '4.0', '<' ) ) {
            add_action( 'admin_notices', function() {
                echo '<div class="error"><p><strong>Rank4Win Error:</strong> This plugin requires WordPress 4.0 or higher. Please update WordPress.</p></div>';
            });
            return; // Stop loading the plugin
        }     
 
    /**
     * Récupération des constante définit
     */ 
        require_once("_inc/config.php");

     /**
     * Chargement des fichiers langue
     */
          function r4w_load_textdomain() {
               load_plugin_textdomain( 'app_rank4win', false, r4w_plugin_name. '/languages' );
          }
          add_action( 'init', 'r4w_load_textdomain' );

    /**
     * Récupération automatique des fonctions
     * FIXED: Added error checking for file loading
     */
          $fcnt_files = glob( dirname(__FILE__)."/_inc/fcnt/fcnt.*.php");
          if ($fcnt_files !== false) {
              foreach ($fcnt_files as $require_file) {
                   if (is_file($require_file) && is_readable($require_file)) {
                        $loaded = @include_once($require_file);
                        if ($loaded === false) {
                            error_log('Rank4Win: Failed to load function file: ' . $require_file);
                        }
                   }
              }
          }

    /**
     * Récupération automatique des librairies
     * FIXED: Added error checking for library loading
     */
          $lib_files = glob( dirname(__FILE__)."/_inc/lib/*/lib.init.php");
          if ($lib_files !== false) {
              foreach ($lib_files as $require_file) {
                   if (is_file($require_file) && is_readable($require_file)) {
                        $loaded = @include_once($require_file);
                        if ($loaded === false) {
                            error_log('Rank4Win: Failed to load library file: ' . $require_file);
                        }
                   }
              }
          }

    /**
     * Récupération automatique des class
     * FIXED: Added error checking for class loading
     */
          $class_files = glob( dirname(__FILE__)."/_inc/class/class.*.php");
          if ($class_files !== false) {
              foreach ($class_files as $require_file) {
                   if (is_file($require_file) && is_readable($require_file)) {
                        $loaded = @include_once($require_file);
                        if ($loaded === false) {
                            error_log('Rank4Win: Failed to load class file: ' . $require_file);
                        }
                   }
              }
          }
